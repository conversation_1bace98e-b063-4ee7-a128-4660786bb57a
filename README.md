# Vue 2 中国行政区划地图组件

一个功能强大的 Vue 2 交互式中国行政区划地图组件，支持省市县三级钻取、热力图叠加和天地图底图。

## 特性

- 🗺️ **交互式地图**: 支持点击省份钻取到市县级别
- 🔥 **热力图支持**: 可叠加热力图数据可视化
- 🎨 **美观样式**: 仿天地图行政区划样式设计
- 📱 **响应式设计**: 适配不同屏幕尺寸
- ⚡ **高性能**: 优化的数据加载和渲染
- 🛠️ **易于集成**: 简单的 API 设计

## 快速开始

### 安装依赖

```bash
npm install vue@^2.7.14 leaflet@^1.9.4 leaflet.heat@^0.2.0
```

### 基本使用

```vue
<template>
  <div>
    <ChinaMap 
      :height="600"
      :tianditu-key="your-tianditu-key"
      :heatmap-data="heatmapData"
      @province-click="onProvinceClick"
      @city-click="onCityClick"
    />
  </div>
</template>

<script>
import ChinaMap from './components/ChinaMap.vue'

export default {
  components: {
    ChinaMap
  },
  data() {
    return {
      heatmapData: [
        { lat: 39.9042, lng: 116.4074, intensity: 0.8 }, // 北京
        { lat: 31.2304, lng: 121.4737, intensity: 0.9 }, // 上海
        // 更多数据点...
      ]
    }
  },
  methods: {
    onProvinceClick(province) {
      console.log('省份点击:', province)
    },
    onCityClick(city) {
      console.log('城市点击:', city)
    }
  }
}
</script>
```

## API 文档

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `height` | Number | 600 | 地图容器高度（像素） |
| `tianditu-key` | String | '' | 天地图 API 密钥 |
| `heatmap-data` | Array | [] | 热力图数据数组 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `province-click` | province | 省份点击事件 |
| `city-click` | city | 城市点击事件 |

### 热力图数据格式

```javascript
const heatmapData = [
  {
    lat: 39.9042,      // 纬度
    lng: 116.4074,     // 经度
    intensity: 0.8     // 强度值 (0-1)
  }
]
```

### 事件回调参数

#### province-click / city-click

```javascript
{
  name: "北京市",           // 行政区名称
  gb: "156110000",         // 国标行政代码
  levelInfo: {             // 行政级别信息
    level: "province",     // 级别: province/city/county
    province: "11",        // 省份代码
    city: null,           // 城市代码
    county: null          // 县区代码
  },
  coordinates: {           // 点击坐标
    lat: 39.9042,
    lng: 116.4074
  }
}
```

## 高级功能

### 自定义样式

组件支持通过 CSS 变量自定义样式：

```css
.china-map-container {
  --province-color: #3388ff;
  --city-color: #ff7800;
  --county-color: #ff3388;
  --hover-opacity: 0.6;
}
```

### 热力图配置

```vue
<template>
  <ChinaMap 
    :heatmap-data="heatmapData"
    ref="chinaMap"
  />
</template>

<script>
export default {
  mounted() {
    // 访问组件实例进行高级配置
    this.$refs.chinaMap.updateHeatmapSettings()
  }
}
</script>
```

### 编程式导航

```javascript
// 重置到全国视图
this.$refs.chinaMap.resetToNationalView()

// 钻取到指定省份
this.$refs.chinaMap.drillDownToProvince(provinceFeature, levelInfo)
```

## 数据要求

### GeoJSON 文件

组件需要一个包含中国行政区划数据的 GeoJSON 文件，文件应放置在 `public` 目录下，命名为 `中国_县.geojson`。

数据格式要求：
- 每个 feature 必须包含 `properties.name`（行政区名称）
- 每个 feature 必须包含 `properties.gb`（国标行政代码）
- 国标代码格式：156 + 省份代码(2位) + 城市代码(2位) + 县区代码(2位)

### 天地图 API Key

1. 访问 [天地图开发者平台](https://console.tianditu.gov.cn/)
2. 注册账号并创建应用
3. 获取 API Key
4. 在组件中配置 `tianditu-key` 属性

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发

### 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 项目结构

```
src/
├── components/
│   └── ChinaMap.vue          # 主组件
├── utils/
│   ├── geoDataAnalyzer.js    # 地理数据分析工具
│   └── mapStyles.js          # 地图样式工具
├── App.vue                   # 示例应用
└── main.js                   # 入口文件
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0
- 初始版本发布
- 支持省市县三级钻取
- 集成热力图功能
- 天地图底图支持
- 响应式设计
