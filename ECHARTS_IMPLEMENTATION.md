# ECharts 中国行政区划地图组件

## 🎯 项目概述

基于 ECharts 5.x 和 Vue 2.7 开发的交互式中国行政区划地图组件，支持省市县三级钻取、热力图叠加等功能。

## ✅ 已实现功能

### 核心功能
- 🗺️ **纯 GeoJSON 地图渲染** - 使用本地 GeoJSON 数据，无需外部地图服务
- 🎯 **三级行政区划钻取** - 支持省→市→县三级钻取导航
- 🔥 **热力图数据叠加** - 可自定义热力图数据点和样式
- 📍 **智能地名标注** - 自动显示行政区名称
- 🎨 **仿天地图样式** - 海洋蓝背景，浅黄色陆地填充

### 交互功能
- 🖱️ **悬停高亮效果** - 鼠标悬停时区域高亮
- 👆 **点击钻取功能** - 点击省份可钻取到市县级
- 🧭 **面包屑导航** - 显示当前位置层级
- ⬅️ **返回导航** - 支持逐级返回和直接回到全国视图
- 🎛️ **热力图控制** - 可调节热力图显示/隐藏和强度

### 技术特性
- ⚡ **高性能渲染** - ECharts 原生渲染引擎
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🔄 **加载状态管理** - 完整的加载进度和错误处理
- 🎨 **自定义样式** - 支持 CSS 变量自定义

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install vue@^2.7.14 echarts@^5.4.3
```

### 2. 基本使用
```vue
<template>
  <div>
    <EChartsMap 
      :height="600"
      :heatmap-data="heatmapData"
      @province-click="onProvinceClick"
      @city-click="onCityClick"
    />
  </div>
</template>

<script>
import EChartsMap from './components/EChartsMap.vue'

export default {
  components: {
    EChartsMap
  },
  data() {
    return {
      heatmapData: [
        { lat: 39.9042, lng: 116.4074, intensity: 0.8 }, // 北京
        { lat: 31.2304, lng: 121.4737, intensity: 0.9 }, // 上海
        // 更多数据点...
      ]
    }
  },
  methods: {
    onProvinceClick(data) {
      console.log('省份点击:', data.name)
    },
    onCityClick(data) {
      console.log('城市点击:', data.name)
    }
  }
}
</script>
```

## 📊 数据格式

### GeoJSON 数据结构
组件使用 `中国_县.geojson` 文件，数据格式：
```json
{
  "features": [
    {
      "geometry": {
        "type": "MultiPolygon",
        "coordinates": [...]
      },
      "properties": {
        "name": "北京市",
        "gb": "156110000"
      }
    }
  ]
}
```

### 热力图数据格式
```javascript
const heatmapData = [
  {
    lat: 39.9042,      // 纬度
    lng: 116.4074,     // 经度  
    intensity: 0.8     // 强度值 (0-1)
  }
]
```

### 国标行政代码规则
- 格式：`156` + 省份代码(2位) + 城市代码(2位) + 县区代码(2位)
- 省级：`156110000` (北京市)
- 市级：`156110100` (北京市市辖区)
- 县级：`156110101` (东城区)

## 🎛️ API 文档

### Props
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `height` | Number | 600 | 地图容器高度（像素） |
| `heatmap-data` | Array | [] | 热力图数据数组 |

### Events
| 事件名 | 参数 | 说明 |
|--------|------|------|
| `province-click` | `{name, level}` | 省份点击事件 |
| `city-click` | `{name, level}` | 城市点击事件 |

### 方法
| 方法名 | 参数 | 说明 |
|--------|------|------|
| `resetToNationalView()` | - | 重置到全国视图 |
| `goBack()` | - | 返回上一级 |

## 🎨 样式自定义

### CSS 变量
```css
.echarts-map-container {
  --background-color: #a8d8ea;
  --land-color: #fef7e6;
  --border-color: #8b7355;
  --hover-color: #fff2cc;
}
```

### 控件样式
```css
.map-controls .control-button {
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px 12px;
}

.breadcrumb-nav {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 4px;
  padding: 8px 12px;
}
```

## 🔧 技术实现

### 核心技术栈
- **Vue 2.7** - 组件框架
- **ECharts 5.x** - 地图渲染引擎
- **GeoJSON** - 地理数据格式
- **CSS3** - 样式和动画

### 数据处理流程
1. **加载 GeoJSON** - 从 public 目录加载地理数据
2. **数据分析** - 根据国标代码分析行政级别
3. **地图注册** - 将 GeoJSON 数据注册到 ECharts
4. **渲染地图** - 使用 ECharts geo 组件渲染
5. **事件绑定** - 绑定点击、悬停等交互事件

### 钻取实现原理
```javascript
// 省级数据过滤
const provinceFeatures = features.filter(feature => {
  const gb = feature.properties.gb
  return gb.substring(7, 9) === '00' && gb.substring(5, 7) === '00'
})

// 市县级数据过滤
const cityFeatures = features.filter(feature => {
  const gb = feature.properties.gb
  return gb.substring(3, 5) === provinceCode && gb.substring(7, 9) !== '00'
})
```

## 🎯 使用场景

### 数据可视化
- 人口分布热力图
- 经济指标地图
- 疫情数据展示
- 销售区域分析

### 地理信息系统
- 行政区划查询
- 区域选择器
- 地址定位工具
- 统计数据展示

## 🔍 故障排除

### 常见问题

1. **地图不显示**
   - 检查 GeoJSON 文件是否在 public 目录
   - 确认文件路径为 `/中国_县.geojson`
   - 检查浏览器控制台错误信息

2. **钻取功能异常**
   - 确认 GeoJSON 数据包含完整的省市县数据
   - 检查国标代码格式是否正确
   - 验证数据过滤逻辑

3. **热力图不显示**
   - 检查热力图数据格式
   - 确认坐标系统为 WGS84
   - 验证强度值范围 (0-1)

### 性能优化

1. **数据优化**
   - 简化 GeoJSON 几何精度
   - 按需加载不同级别数据
   - 使用数据缓存

2. **渲染优化**
   - 合理设置地图缩放级别
   - 控制热力图数据点数量
   - 使用防抖处理交互事件

## 📈 扩展功能

### 计划中的功能
- 🔍 搜索定位功能
- 📊 数据统计面板
- 📤 地图导出功能
- 🌐 多语言支持
- 🎨 主题切换功能

### 自定义扩展
```javascript
// 添加自定义图层
option.series.push({
  type: 'scatter',
  coordinateSystem: 'geo',
  data: customData,
  symbolSize: 10
})

// 自定义样式
option.geo.itemStyle = {
  areaColor: '#custom-color',
  borderColor: '#custom-border'
}
```

## 📝 更新日志

### v1.0.0 (2024-06-25)
- ✅ 完成 ECharts 基础地图组件
- ✅ 实现省市县三级钻取
- ✅ 集成热力图功能
- ✅ 添加交互控制面板
- ✅ 完善错误处理机制

## 📄 许可证

MIT License - 详见 LICENSE 文件
