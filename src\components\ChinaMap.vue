<template>
  <div class="china-map-container">
    <!-- Loading overlay -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>{{ loadingMessage }}</p>
      <div class="loading-progress" v-if="loadingProgress > 0">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: loadingProgress + '%' }"></div>
        </div>
        <span class="progress-text">{{ loadingProgress }}%</span>
      </div>
    </div>

    <!-- Error overlay -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h3>{{ errorTitle }}</h3>
        <p>{{ errorMessage }}</p>
        <div class="error-details" v-if="errorDetails">
          <details>
            <summary>技术详情</summary>
            <pre>{{ errorDetails }}</pre>
          </details>
        </div>
        <div class="error-actions">
          <button @click="retryLoad" class="retry-button">重试加载</button>
          <button @click="loadFallbackData" class="fallback-button" v-if="!fallbackMode">使用离线数据</button>
          <button @click="resetComponent" class="reset-button">重置组件</button>
        </div>
      </div>
    </div>
    
    <!-- Map container -->
    <div 
      ref="mapContainer" 
      class="map-container"
      :style="{ height: height + 'px' }"
    ></div>
    
    <!-- Map controls -->
    <div class="map-controls">
      <button
        v-if="currentLevel !== 'national'"
        @click="goBack"
        class="control-button back-button"
        title="返回上一级"
      >
        ← 返回
      </button>

      <button
        @click="resetToNationalView"
        class="control-button home-button"
        title="返回全国视图"
      >
        🏠 全国
      </button>

      <div class="level-indicator">
        当前层级: {{ levelText }}
      </div>
    </div>

    <!-- Breadcrumb navigation -->
    <div class="breadcrumb-nav" v-if="breadcrumbs.length > 1">
      <span
        v-for="(crumb, index) in breadcrumbs"
        :key="index"
        class="breadcrumb-item"
        :class="{ active: index === breadcrumbs.length - 1 }"
        @click="navigateToBreadcrumb(index)"
      >
        {{ crumb.name }}
        <span v-if="index < breadcrumbs.length - 1" class="breadcrumb-separator">></span>
      </span>
    </div>

    <!-- Drill-down hint -->
    <div class="drill-hint" v-if="showDrillHint">
      <div class="hint-content">
        <span class="hint-icon">👆</span>
        <span class="hint-text">点击{{ getDrillHintText() }}可查看详细信息</span>
        <button @click="showDrillHint = false" class="hint-close">×</button>
      </div>
    </div>

    <!-- Heatmap controls -->
    <div class="heatmap-controls" v-if="heatmapData.length > 0">
      <div class="heatmap-toggle">
        <label class="toggle-label">
          <input
            type="checkbox"
            v-model="heatmapVisible"
            @change="toggleHeatmap"
          />
          <span class="toggle-text">热力图</span>
        </label>
      </div>

      <div class="heatmap-settings" v-if="heatmapVisible">
        <div class="setting-group">
          <label>强度:</label>
          <input
            type="range"
            min="0.1"
            max="2"
            step="0.1"
            v-model="heatmapIntensity"
            @input="updateHeatmapSettings"
          />
          <span class="setting-value">{{ heatmapIntensity }}</span>
        </div>

        <div class="setting-group">
          <label>半径:</label>
          <input
            type="range"
            min="10"
            max="50"
            step="5"
            v-model="heatmapRadius"
            @input="updateHeatmapSettings"
          />
          <span class="setting-value">{{ heatmapRadius }}</span>
        </div>

        <div class="setting-group">
          <label>主题:</label>
          <select v-model="heatmapTheme" @change="updateHeatmapSettings">
            <option value="default">默认</option>
            <option value="blue">蓝色</option>
            <option value="green">绿色</option>
            <option value="red">红色</option>
          </select>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import 'leaflet.heat'

// Fix for default markers in Leaflet with Vite
import markerIcon2x from 'leaflet/dist/images/marker-icon-2x.png'
import markerIcon from 'leaflet/dist/images/marker-icon.png'
import markerShadow from 'leaflet/dist/images/marker-shadow.png'

delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: markerIcon2x,
  iconUrl: markerIcon,
  shadowUrl: markerShadow,
})

import {
  groupFeaturesByLevel,
  filterByProvince,
  getUniqueProvinces,
  calculateBounds,
  parseGBCode
} from '../utils/geoDataAnalyzer.js'

import {
  getAdministrativeStyle,
  getHeatmapGradient,
  getPopupContent,
  getPopupStyles,
  calculateZoomLevel,
  getAnimationOptions
} from '../utils/mapStyles.js'

export default {
  name: 'ChinaMap',
  props: {
    height: {
      type: Number,
      default: 600
    },
    heatmapData: {
      type: Array,
      default: () => []
    },
    tiandituKey: {
      type: String,
      default: '' // Users should provide their own Tianditu API key
    }
  },
  data() {
    return {
      map: null,
      loading: true,
      error: null,
      errorTitle: '',
      errorMessage: '',
      errorDetails: '',
      loadingMessage: '初始化地图组件...',
      loadingProgress: 0,
      fallbackMode: false,
      geoData: null,
      groupedData: null,
      currentLevel: 'national', // 'national', 'province', 'city'
      currentProvince: null,
      currentCity: null,
      administrativeLayers: [],
      labelLayers: [],
      heatmapLayer: null,
      nationalBounds: null,
      showDrillHint: true,
      navigationHistory: [],
      heatmapVisible: true,
      heatmapIntensity: 1.0,
      heatmapRadius: 25,
      heatmapTheme: 'default'
    }
  },
  computed: {
    levelText() {
      switch (this.currentLevel) {
        case 'national': return '全国'
        case 'province': return this.currentProvince?.name || '省级'
        case 'city': return this.currentCity?.name || '市级'
        default: return '未知'
      }
    },
    breadcrumbs() {
      const crumbs = [{ name: '全国', level: 'national' }]

      if (this.currentProvince) {
        crumbs.push({
          name: this.currentProvince.name,
          level: 'province',
          data: this.currentProvince
        })
      }

      if (this.currentCity) {
        crumbs.push({
          name: this.currentCity.name,
          level: 'city',
          data: this.currentCity
        })
      }

      return crumbs
    }
  },
  mounted() {
    this.initializeMap()
    this.loadGeoData()
    this.injectPopupStyles()
  },
  beforeDestroy() {
    if (this.map) {
      this.map.remove()
    }
  },
  watch: {
    heatmapData: {
      handler(newData) {
        this.updateHeatmap(newData)
      },
      deep: true
    }
  },
  methods: {
    initializeMap() {
      // Initialize Leaflet map without any base layer
      this.map = L.map(this.$refs.mapContainer, {
        center: [35.8617, 104.1954], // Center of China
        zoom: 4,
        minZoom: 3,
        maxZoom: 10,
        zoomControl: true,
        attributionControl: false // Remove attribution control
      })

      // Set map background to ocean blue color like Tianditu
      this.$refs.mapContainer.style.backgroundColor = '#a8d8ea'

      // Add custom CSS for better styling
      this.addMapStyles()

      // Set initial view to China bounds
      this.map.setView([35.8617, 104.1954], 4)

      // Listen for zoom events to update heatmap
      this.map.on('zoomend', () => {
        if (this.heatmapLayer && this.heatmapVisible) {
          this.updateHeatmapSettings()
        }
      })
    },

    async loadGeoData() {
      try {
        this.loading = true
        this.error = null
        this.loadingProgress = 0
        this.loadingMessage = '正在加载地理数据...'

        // Simulate progress for better UX
        this.loadingProgress = 10

        // Load the GeoJSON file
        const response = await fetch('/中国_县.geojson')
        this.loadingProgress = 30

        if (!response.ok) {
          throw new Error(`无法加载地理数据文件 (HTTP ${response.status})`)
        }

        this.loadingMessage = '正在解析地理数据...'
        this.loadingProgress = 50

        const data = await response.json()
        this.loadingProgress = 70

        if (!data || !data.features || !Array.isArray(data.features)) {
          throw new Error('地理数据格式不正确')
        }

        this.geoData = data
        this.groupedData = groupFeaturesByLevel(this.geoData.features)
        this.loadingProgress = 80

        this.loadingMessage = '正在计算地图边界...'
        // Calculate national bounds
        this.nationalBounds = calculateBounds(this.geoData.features)
        this.loadingProgress = 90

        this.loadingMessage = '正在渲染地图...'
        // Load initial administrative boundaries
        this.loadAdministrativeBoundaries()

        // Fit map to China bounds
        if (this.nationalBounds) {
          this.map.fitBounds([
            [this.nationalBounds[1], this.nationalBounds[0]],
            [this.nationalBounds[3], this.nationalBounds[2]]
          ], { padding: [20, 20] })
        }

        // Initialize heatmap if data is provided
        if (this.heatmapData.length > 0) {
          this.updateHeatmap(this.heatmapData)
        }

        this.loadingProgress = 100
        setTimeout(() => {
          this.loading = false
        }, 300) // Small delay to show 100% completion

      } catch (err) {
        this.handleError('数据加载失败', err.message, err.stack)
        console.error('Error loading geo data:', err)
      }
    },

    loadAdministrativeBoundaries() {
      // Clear existing layers
      this.clearAdministrativeLayers()
      
      let featuresToShow = []
      
      switch (this.currentLevel) {
        case 'national':
          // Show province-level boundaries
          featuresToShow = this.groupedData.province
          break
        case 'province':
          // Show city/county level for current province
          if (this.currentProvince) {
            featuresToShow = filterByProvince(
              [...this.groupedData.city, ...this.groupedData.county],
              this.currentProvince.code
            )
          }
          break
      }
      
      // Add features to map
      featuresToShow.forEach(feature => {
        this.addFeatureToMap(feature)
      })
    },

    addFeatureToMap(feature) {
      const layer = L.geoJSON(feature, {
        style: this.getFeatureStyle(feature),
        onEachFeature: (feature, layer) => {
          this.bindFeatureEvents(feature, layer)
        }
      })

      layer.addTo(this.map)
      this.administrativeLayers.push(layer)

      // Add label for the feature
      this.addFeatureLabel(feature)
    },

    getFeatureStyle(feature, state = 'default') {
      const levelInfo = parseGBCode(feature.properties.gb)
      return getAdministrativeStyle(levelInfo.level, state)
    },

    bindFeatureEvents(feature, layer) {
      const levelInfo = parseGBCode(feature.properties.gb)

      layer.on({
        mouseover: (e) => {
          const layer = e.target
          layer.setStyle(this.getFeatureStyle(feature, 'hover'))

          // Show tooltip with enhanced content
          layer.bindTooltip(feature.properties.name, {
            permanent: false,
            direction: 'top',
            className: 'custom-tooltip'
          }).openTooltip()
        },
        mouseout: (e) => {
          const layer = e.target
          layer.setStyle(this.getFeatureStyle(feature, 'default'))
          layer.closeTooltip()
        },
        click: (e) => {
          // Show popup with detailed information
          layer.bindPopup(getPopupContent(feature, levelInfo), {
            maxWidth: 300,
            className: 'custom-popup'
          }).openPopup()

          this.handleFeatureClick(feature, e)
        }
      })
    },

    handleFeatureClick(feature, event) {
      const levelInfo = parseGBCode(feature.properties.gb)
      
      if (levelInfo.level === 'province' && this.currentLevel === 'national') {
        // Drill down to province level
        this.drillDownToProvince(feature, levelInfo)
      } else if (levelInfo.level === 'city' && this.currentLevel === 'province') {
        // Drill down to city level
        this.drillDownToCity(feature, levelInfo)
      }
      
      // Emit events
      this.$emit(`${levelInfo.level}-click`, {
        name: feature.properties.name,
        gb: feature.properties.gb,
        levelInfo,
        coordinates: event.latlng
      })
    },

    drillDownToProvince(feature, levelInfo) {
      // Add to navigation history
      this.addToNavigationHistory('province', feature, levelInfo)

      this.currentLevel = 'province'
      this.currentProvince = {
        name: feature.properties.name,
        code: levelInfo.province,
        gb: feature.properties.gb
      }

      // Calculate appropriate zoom and bounds
      const provinceBounds = calculateBounds([feature])
      const zoom = calculateZoomLevel('province', provinceBounds)
      const animationOptions = getAnimationOptions('drillDown')

      // Animate to province bounds
      this.map.fitBounds([
        [provinceBounds[1], provinceBounds[0]],
        [provinceBounds[3], provinceBounds[2]]
      ], {
        ...animationOptions,
        maxZoom: zoom
      })

      // Reload boundaries for province level
      setTimeout(() => {
        this.loadAdministrativeBoundaries()
      }, animationOptions.duration * 1000)
    },

    drillDownToCity(feature, levelInfo) {
      // Add to navigation history
      this.addToNavigationHistory('city', feature, levelInfo)

      this.currentLevel = 'city'
      this.currentCity = {
        name: feature.properties.name,
        code: levelInfo.city,
        gb: feature.properties.gb
      }

      // Calculate appropriate zoom and bounds
      const cityBounds = calculateBounds([feature])
      const zoom = calculateZoomLevel('city', cityBounds)
      const animationOptions = getAnimationOptions('drillDown')

      // Animate to city bounds
      this.map.fitBounds([
        [cityBounds[1], cityBounds[0]],
        [cityBounds[3], cityBounds[2]]
      ], {
        ...animationOptions,
        maxZoom: zoom
      })
    },

    goBack() {
      if (this.currentLevel === 'city') {
        this.currentLevel = 'province'
        this.currentCity = null
        this.loadAdministrativeBoundaries()
      } else if (this.currentLevel === 'province') {
        this.resetToNationalView()
      }
    },

    resetToNationalView() {
      this.currentLevel = 'national'
      this.currentProvince = null
      this.currentCity = null

      const animationOptions = getAnimationOptions('reset')

      // Reset map view to national bounds with animation
      if (this.nationalBounds) {
        this.map.fitBounds([
          [this.nationalBounds[1], this.nationalBounds[0]],
          [this.nationalBounds[3], this.nationalBounds[2]]
        ], {
          ...animationOptions,
          maxZoom: 6
        })
      } else {
        this.map.setView([35.8617, 104.1954], 4, animationOptions)
      }

      // Reload boundaries after animation
      setTimeout(() => {
        this.loadAdministrativeBoundaries()
      }, animationOptions.duration * 1000)
    },

    clearAdministrativeLayers() {
      this.administrativeLayers.forEach(layer => {
        this.map.removeLayer(layer)
      })
      this.administrativeLayers = []

      // Clear labels
      this.labelLayers.forEach(layer => {
        this.map.removeLayer(layer)
      })
      this.labelLayers = []
    },

    updateHeatmap(data) {
      if (!this.map || !data || data.length === 0) {
        // Remove existing heatmap if no data
        if (this.heatmapLayer) {
          this.map.removeLayer(this.heatmapLayer)
          this.heatmapLayer = null
        }
        return
      }

      // Remove existing heatmap
      if (this.heatmapLayer) {
        this.map.removeLayer(this.heatmapLayer)
      }

      // Convert data to format expected by leaflet.heat
      const heatmapPoints = data.map(point => [
        point.lat,
        point.lng,
        (point.intensity || 0.5) * this.heatmapIntensity
      ])

      // Create heatmap layer with enhanced styling
      this.heatmapLayer = L.heatLayer(heatmapPoints, {
        radius: this.getHeatmapRadius(),
        blur: 15,
        maxZoom: 12,
        gradient: getHeatmapGradient(this.heatmapTheme)
      })

      if (this.heatmapVisible) {
        this.heatmapLayer.addTo(this.map)
      }
    },

    getHeatmapRadius() {
      // Adjust heatmap radius based on current zoom level and user setting
      const zoom = this.map.getZoom()
      let baseRadius = this.heatmapRadius

      // Scale radius based on zoom level
      if (zoom <= 4) baseRadius *= 0.8
      else if (zoom <= 6) baseRadius *= 1.0
      else if (zoom <= 8) baseRadius *= 1.2
      else baseRadius *= 1.4

      return Math.max(10, Math.min(50, baseRadius))
    },

    updateHeatmapSettings() {
      if (this.heatmapData.length > 0) {
        this.updateHeatmap(this.heatmapData)
      }
    },

    toggleHeatmap() {
      if (!this.heatmapLayer) return

      if (this.heatmapVisible) {
        this.heatmapLayer.addTo(this.map)
      } else {
        this.map.removeLayer(this.heatmapLayer)
      }
    },

    retryLoad() {
      this.loadGeoData()
    },

    injectPopupStyles() {
      // Inject popup styles into document head
      if (!document.getElementById('china-map-popup-styles')) {
        const style = document.createElement('style')
        style.id = 'china-map-popup-styles'
        style.textContent = getPopupStyles()
        document.head.appendChild(style)
      }
    },

    addMapStyles() {
      // Add custom map styles for better appearance
      if (!document.getElementById('china-map-custom-styles')) {
        const style = document.createElement('style')
        style.id = 'china-map-custom-styles'
        style.textContent = `
          .leaflet-container {
            background: #a8d8ea !important;
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
          }

          .leaflet-control-zoom {
            border: none !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
          }

          .leaflet-control-zoom a {
            background: white !important;
            color: #333 !important;
            border: none !important;
            font-size: 16px !important;
            font-weight: bold !important;
          }

          .leaflet-control-zoom a:hover {
            background: #f0f0f0 !important;
          }

          .leaflet-popup-content-wrapper {
            border-radius: 8px !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
          }

          .leaflet-tooltip {
            background: rgba(0,0,0,0.8) !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            font-size: 12px !important;
            font-weight: 500 !important;
          }

          .feature-label {
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
          }

          .feature-label span {
            display: block;
            text-align: center;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
            white-space: nowrap;
            pointer-events: none;
          }

          .feature-label-province span {
            font-size: 14px;
            color: #2d3748;
          }

          .feature-label-city span {
            font-size: 12px;
            color: #4a5568;
          }

          .feature-label-county span {
            font-size: 10px;
            color: #718096;
          }
        `
        document.head.appendChild(style)
      }
    },

    addFeatureLabel(feature) {
      // Calculate the center point of the feature for label placement
      const bounds = this.calculateFeatureBounds(feature)
      if (!bounds) return

      const centerLat = (bounds[1] + bounds[3]) / 2
      const centerLng = (bounds[0] + bounds[2]) / 2
      const levelInfo = parseGBCode(feature.properties.gb)

      // Create label with appropriate styling based on level
      const labelIcon = L.divIcon({
        className: `feature-label feature-label-${levelInfo.level}`,
        html: `<span>${feature.properties.name}</span>`,
        iconSize: [100, 20],
        iconAnchor: [50, 10]
      })

      const labelMarker = L.marker([centerLat, centerLng], {
        icon: labelIcon,
        interactive: false // Labels should not be interactive
      })

      labelMarker.addTo(this.map)
      this.labelLayers.push(labelMarker)
    },

    calculateFeatureBounds(feature) {
      try {
        let minLng = Infinity, minLat = Infinity
        let maxLng = -Infinity, maxLat = -Infinity

        const processCoords = (coords) => {
          if (Array.isArray(coords[0])) {
            coords.forEach(processCoords)
          } else {
            const [lng, lat] = coords
            minLng = Math.min(minLng, lng)
            minLat = Math.min(minLat, lat)
            maxLng = Math.max(maxLng, lng)
            maxLat = Math.max(maxLat, lat)
          }
        }

        processCoords(feature.geometry.coordinates)
        return [minLng, minLat, maxLng, maxLat]
      } catch (error) {
        console.warn('Error calculating feature bounds:', error)
        return null
      }
    },

    handleError(title, message, details = null) {
      this.loading = false
      this.errorTitle = title
      this.errorMessage = message
      this.errorDetails = details
      this.error = true
    },

    loadFallbackData() {
      // Try to load a simplified version or show a message
      this.errorMessage = '正在尝试加载备用数据...'
      this.fallbackMode = true
      // You could implement fallback data loading here
      setTimeout(() => {
        this.error = false
        this.errorMessage = '无法加载地图数据，请检查网络连接或联系管理员'
        this.error = true
      }, 2000)
    },

    resetComponent() {
      // Reset component to initial state
      this.loading = true
      this.error = false
      this.errorTitle = ''
      this.errorMessage = ''
      this.errorDetails = ''
      this.currentLevel = 'national'
      this.currentProvince = null
      this.currentCity = null
      this.fallbackMode = false

      // Clear all layers
      this.clearAdministrativeLayers()
      if (this.heatmapLayer) {
        this.map.removeLayer(this.heatmapLayer)
        this.heatmapLayer = null
      }

      // Retry loading
      setTimeout(() => {
        this.loadGeoData()
      }, 500)
    },

    addToNavigationHistory(level, feature, levelInfo) {
      this.navigationHistory.push({
        level,
        feature,
        levelInfo,
        timestamp: Date.now()
      })

      // Keep only last 10 navigation items
      if (this.navigationHistory.length > 10) {
        this.navigationHistory = this.navigationHistory.slice(-10)
      }
    },

    navigateToBreadcrumb(index) {
      const crumb = this.breadcrumbs[index]

      if (crumb.level === 'national') {
        this.resetToNationalView()
      } else if (crumb.level === 'province' && this.currentLevel !== 'province') {
        // Navigate back to province level
        this.currentLevel = 'province'
        this.currentCity = null
        this.loadAdministrativeBoundaries()
      }
      // If already at the target level, do nothing
    },

    getDrillHintText() {
      switch (this.currentLevel) {
        case 'national': return '省份'
        case 'province': return '城市/区县'
        case 'city': return '详细区域'
        default: return '区域'
      }
    }
  }
}
</script>

<style scoped>
.china-map-container {
  position: relative;
  width: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.map-container {
  width: 100%;
  position: relative;
  z-index: 1;
}

/* Loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* Error overlay */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-content {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 300px;
}

.error-content h3 {
  color: #e74c3c;
  margin: 0 0 10px 0;
  font-size: 18px;
}

.error-content p {
  color: #666;
  margin: 0 0 20px 0;
  font-size: 14px;
}

.retry-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.retry-button:hover {
  background: #2980b9;
}

/* Map controls */
.map-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-button {
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  white-space: nowrap;
}

.control-button:hover {
  background: #f8f9fa;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.back-button {
  background: #17a2b8;
  color: white;
  border-color: #17a2b8;
}

.back-button:hover {
  background: #138496;
  border-color: #117a8b;
}

.home-button {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.home-button:hover {
  background: #218838;
  border-color: #1e7e34;
}

.level-indicator {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  margin-top: 4px;
}

/* Breadcrumb navigation */
.breadcrumb-nav {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  max-width: 300px;
}

.breadcrumb-item {
  cursor: pointer;
  color: #007bff;
  text-decoration: none;
  transition: color 0.3s;
}

.breadcrumb-item:hover {
  color: #0056b3;
  text-decoration: underline;
}

.breadcrumb-item.active {
  color: #6c757d;
  cursor: default;
  font-weight: 600;
}

.breadcrumb-item.active:hover {
  text-decoration: none;
}

.breadcrumb-separator {
  margin: 0 6px;
  color: #6c757d;
}

/* Drill hint */
.drill-hint {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  animation: fadeInUp 0.5s ease-out;
}

.hint-content {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  max-width: 280px;
}

.hint-icon {
  font-size: 16px;
}

.hint-text {
  flex: 1;
}

.hint-close {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.hint-close:hover {
  opacity: 1;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Heatmap controls */
.heatmap-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 12px;
  min-width: 200px;
}

.heatmap-toggle {
  margin-bottom: 12px;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.toggle-label input[type="checkbox"] {
  margin: 0;
}

.heatmap-settings {
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
}

.setting-group {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
}

.setting-group label {
  min-width: 40px;
  color: #666;
  font-weight: 500;
}

.setting-group input[type="range"] {
  flex: 1;
  margin: 0;
}

.setting-group select {
  flex: 1;
  padding: 2px 4px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-size: 12px;
}

.setting-value {
  min-width: 30px;
  text-align: right;
  color: #333;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .map-controls {
    top: 5px;
    left: 5px;
  }

  .control-button {
    padding: 6px 10px;
    font-size: 11px;
  }

  .level-indicator {
    font-size: 11px;
    padding: 4px 8px;
  }
}

/* Custom leaflet popup styles */
.china-map-container :deep(.leaflet-popup-content-wrapper) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.china-map-container :deep(.leaflet-popup-content) {
  margin: 12px 16px;
  font-size: 14px;
  line-height: 1.4;
}

/* Custom tooltip styles */
.china-map-container :deep(.leaflet-tooltip) {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.china-map-container :deep(.leaflet-tooltip-top:before) {
  border-top-color: rgba(0, 0, 0, 0.8);
}
</style>
