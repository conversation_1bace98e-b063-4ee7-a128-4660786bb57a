<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vue China Map Demo</title>
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }
    .demo-container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .demo-header {
      padding: 20px;
      background: #2c3e50;
      color: white;
    }
    .demo-header h1 {
      margin: 0;
      font-size: 24px;
    }
    .demo-header p {
      margin: 10px 0 0 0;
      opacity: 0.8;
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <div class="demo-header">
      <h1>Interactive Chinese Administrative Map</h1>
      <p>Click on provinces to drill down to city/county level. Features heatmap overlay and Tianditu base layer.</p>
    </div>
    <div id="app"></div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>
</html>
