<template>
  <div class="simple-map-container">
    <div v-if="loading" class="loading">加载中...</div>
    <div v-if="error" class="error">{{ error }}</div>
    <div ref="mapContainer" class="map" style="width: 100%; height: 600px;"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'SimpleMap',
  data() {
    return {
      chart: null,
      loading: true,
      error: null
    }
  },
  mounted() {
    this.initMap()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    async initMap() {
      try {
        console.log('开始初始化简单地图')
        
        // 初始化图表
        this.chart = echarts.init(this.$refs.mapContainer)
        
        // 加载 GeoJSON 数据
        const response = await fetch('/中国_县.geojson')
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }
        
        const geoData = await response.json()
        console.log('GeoJSON 数据加载成功，特征数:', geoData.features.length)
        
        // 注册地图
        echarts.registerMap('china', geoData)
        console.log('地图注册成功')
        
        // 创建省级地图数据
        const provinceData = this.createProvinceData(geoData)
        echarts.registerMap('china', provinceData)

        // 配置选项
        const option = {
          backgroundColor: '#a8d8ea',
          geo: {
            map: 'china',
            roam: true,
            zoom: 1.2,
            center: [104.1954, 35.8617],
            itemStyle: {
              areaColor: '#fef7e6',
              borderColor: '#8b7355',
              borderWidth: 1.5
            },
            emphasis: {
              itemStyle: {
                areaColor: '#fff2cc',
                borderColor: '#6b5b47',
                borderWidth: 2
              },
              label: {
                show: true,
                color: '#333',
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            label: {
              show: true,
              color: '#333',
              fontSize: 12,
              fontWeight: 'normal'
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}<br/>点击查看详情'
          }
        }
        
        console.log('设置图表选项')
        this.chart.setOption(option)
        
        this.loading = false
        console.log('地图初始化完成')
        
      } catch (err) {
        console.error('地图初始化失败:', err)
        this.error = err.message
        this.loading = false
      }
    },

    createProvinceData(geoData) {
      const provinceNames = {
        '11': '北京市', '12': '天津市', '13': '河北省', '14': '山西省', '15': '内蒙古自治区',
        '21': '辽宁省', '22': '吉林省', '23': '黑龙江省', '31': '上海市', '32': '江苏省',
        '33': '浙江省', '34': '安徽省', '35': '福建省', '36': '江西省', '37': '山东省',
        '41': '河南省', '42': '湖北省', '43': '湖南省', '44': '广东省', '45': '广西壮族自治区',
        '46': '海南省', '50': '重庆市', '51': '四川省', '52': '贵州省', '53': '云南省',
        '54': '西藏自治区', '61': '陕西省', '62': '甘肃省', '63': '青海省', '64': '宁夏回族自治区',
        '65': '新疆维吾尔自治区', '71': '台湾省', '81': '香港特别行政区', '82': '澳门特别行政区'
      }

      const provinceMap = new Map()

      // 按省份分组县级数据
      geoData.features.forEach(feature => {
        const gb = feature.properties.gb
        if (gb && gb.length >= 5) {
          const provinceCode = gb.substring(3, 5)
          if (provinceNames[provinceCode]) {
            if (!provinceMap.has(provinceCode)) {
              provinceMap.set(provinceCode, {
                type: 'Feature',
                properties: {
                  name: provinceNames[provinceCode],
                  gb: `156${provinceCode}0000`
                },
                geometry: {
                  type: 'MultiPolygon',
                  coordinates: []
                }
              })
            }

            // 收集该省的所有几何形状
            const province = provinceMap.get(provinceCode)
            if (feature.geometry && feature.geometry.coordinates) {
              if (feature.geometry.type === 'Polygon') {
                province.geometry.coordinates.push(feature.geometry.coordinates)
              } else if (feature.geometry.type === 'MultiPolygon') {
                province.geometry.coordinates.push(...feature.geometry.coordinates)
              }
            }
          }
        }
      })

      const provinceFeatures = Array.from(provinceMap.values()).filter(p =>
        p.geometry.coordinates.length > 0
      )

      console.log('创建省级数据，省份数:', provinceFeatures.length)
      console.log('省份列表:', provinceFeatures.map(p => p.properties.name))

      return {
        type: 'FeatureCollection',
        features: provinceFeatures
      }
    }
  }
}
</script>

<style scoped>
.simple-map-container {
  position: relative;
  width: 100%;
  height: 600px;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.loading, .error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  padding: 20px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.error {
  color: #e74c3c;
}

.map {
  width: 100%;
  height: 100%;
}
</style>
