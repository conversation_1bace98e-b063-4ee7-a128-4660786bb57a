<template>
  <div class="simple-map-container">
    <div v-if="loading" class="loading">加载中...</div>
    <div v-if="error" class="error">{{ error }}</div>
    <div ref="mapContainer" class="map" style="width: 100%; height: 600px;"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'SimpleMap',
  data() {
    return {
      chart: null,
      loading: true,
      error: null
    }
  },
  mounted() {
    this.initMap()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    async initMap() {
      try {
        console.log('开始初始化简单地图')
        
        // 初始化图表
        this.chart = echarts.init(this.$refs.mapContainer)
        
        // 加载 GeoJSON 数据
        const response = await fetch('/中国_县.geojson')
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }
        
        const geoData = await response.json()
        console.log('GeoJSON 数据加载成功，特征数:', geoData.features.length)
        
        // 注册地图
        echarts.registerMap('china', geoData)
        console.log('地图注册成功')
        
        // 配置选项
        const option = {
          backgroundColor: '#a8d8ea',
          geo: {
            map: 'china',
            roam: true,
            zoom: 1.2,
            center: [104.1954, 35.8617],
            itemStyle: {
              areaColor: '#fef7e6',
              borderColor: '#8b7355',
              borderWidth: 0.5
            },
            emphasis: {
              itemStyle: {
                areaColor: '#fff2cc'
              }
            },
            label: {
              show: false
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}'
          }
        }
        
        console.log('设置图表选项')
        this.chart.setOption(option)
        
        this.loading = false
        console.log('地图初始化完成')
        
      } catch (err) {
        console.error('地图初始化失败:', err)
        this.error = err.message
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.simple-map-container {
  position: relative;
  width: 100%;
  height: 600px;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.loading, .error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  padding: 20px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.error {
  color: #e74c3c;
}

.map {
  width: 100%;
  height: 100%;
}
</style>
