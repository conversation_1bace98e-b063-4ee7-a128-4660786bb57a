<template>
  <div id="app">
    <h2>简单地图测试</h2>
    <SimpleMap />

    <h2 style="margin-top: 20px;">完整功能地图</h2>
    <EChartsMap
      :height="600"
      :heatmap-data="sampleHeatmapData"
      @province-click="onProvinceClick"
      @city-click="onCityClick"
    />
    <div class="controls" v-if="selectedProvince">
      <button @click="resetView">返回全国视图</button>
      <p>当前选中: {{ selectedProvince }}</p>
    </div>
  </div>
</template>

<script>
import EChartsMap from './components/EChartsMap.vue'
import SimpleMap from './components/SimpleMap.vue'

export default {
  name: 'App',
  components: {
    EChartsMap,
    SimpleMap
  },
  data() {
    return {
      selectedProvince: null,
      sampleHeatmapData: [
        { lat: 39.9042, lng: 116.4074, intensity: 0.8 }, // Beijing
        { lat: 31.2304, lng: 121.4737, intensity: 0.9 }, // Shanghai
        { lat: 23.1291, lng: 113.2644, intensity: 0.7 }, // Guangzhou
        { lat: 30.5728, lng: 104.0668, intensity: 0.6 }, // Chengdu
        { lat: 29.5630, lng: 106.5516, intensity: 0.5 }, // Chongqing
        { lat: 38.0428, lng: 114.5149, intensity: 0.4 }, // Shijiazhuang
        { lat: 36.0611, lng: 103.8343, intensity: 0.3 }, // Lanzhou
        { lat: 43.8256, lng: 87.6168, intensity: 0.4 }, // Urumqi
        { lat: 25.0389, lng: 102.7183, intensity: 0.5 }, // Kunming
        { lat: 26.5783, lng: 106.7135, intensity: 0.3 }  // Guiyang
      ]
    }
  },
  methods: {
    onProvinceClick(province) {
      this.selectedProvince = province.name
      console.log('Province clicked:', province)
    },
    onCityClick(city) {
      console.log('City clicked:', city)
    },
    resetView() {
      this.selectedProvince = null
      this.$refs.chinaMap?.resetToNationalView()
    }
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.controls {
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
}

.controls button {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}

.controls button:hover {
  background: #0056b3;
}

.controls p {
  margin: 10px 0 0 0;
  color: #6c757d;
}
</style>
