/**
 * Utility functions for analyzing and processing Chinese administrative GeoJSON data
 */

/**
 * Parse GB code to determine administrative level
 * GB codes follow pattern: 156 + province(2) + city(2) + county(2)
 * @param {string} gbCode - The GB administrative code
 * @returns {object} Administrative level information
 */
export function parseGBCode(gbCode) {
  if (!gbCode || gbCode.length !== 12) {
    return { level: 'unknown', province: null, city: null, county: null }
  }
  
  const province = gbCode.substring(3, 5)
  const city = gbCode.substring(5, 7)
  const county = gbCode.substring(7, 9)
  
  let level = 'county'
  if (county === '00') {
    level = city === '00' ? 'province' : 'city'
  }
  
  return {
    level,
    province,
    city: city !== '00' ? city : null,
    county: county !== '00' ? county : null,
    fullCode: gbCode
  }
}

/**
 * Group features by administrative level
 * @param {Array} features - GeoJSON features array
 * @returns {object} Grouped features by level
 */
export function groupFeaturesByLevel(features) {
  const grouped = {
    province: [],
    city: [],
    county: []
  }
  
  features.forEach(feature => {
    const { name, gb } = feature.properties
    const levelInfo = parseGBCode(gb)
    
    if (grouped[levelInfo.level]) {
      grouped[levelInfo.level].push({
        ...feature,
        levelInfo,
        displayName: name
      })
    }
  })
  
  return grouped
}

/**
 * Get province code from GB code
 * @param {string} gbCode - The GB administrative code
 * @returns {string} Province code
 */
export function getProvinceCode(gbCode) {
  return gbCode ? gbCode.substring(3, 5) : null
}

/**
 * Filter features by province
 * @param {Array} features - GeoJSON features array
 * @param {string} provinceCode - Province code to filter by
 * @returns {Array} Filtered features
 */
export function filterByProvince(features, provinceCode) {
  return features.filter(feature => {
    const gb = feature.properties.gb
    return gb && gb.substring(3, 5) === provinceCode
  })
}

/**
 * Get unique provinces from features
 * @param {Array} features - GeoJSON features array
 * @returns {Array} Array of unique province objects
 */
export function getUniqueProvinces(features) {
  const provinceMap = new Map()
  
  features.forEach(feature => {
    const { name, gb } = feature.properties
    const provinceCode = getProvinceCode(gb)
    
    if (provinceCode && !provinceMap.has(provinceCode)) {
      const levelInfo = parseGBCode(gb)
      if (levelInfo.level === 'province' || !provinceMap.has(provinceCode)) {
        provinceMap.set(provinceCode, {
          code: provinceCode,
          name: levelInfo.level === 'province' ? name : getProvinceNameFromCode(provinceCode),
          gbCode: gb
        })
      }
    }
  })
  
  return Array.from(provinceMap.values())
}

/**
 * Map province codes to names (partial mapping for common provinces)
 * @param {string} code - Province code
 * @returns {string} Province name
 */
function getProvinceNameFromCode(code) {
  const provinceNames = {
    '11': '北京市',
    '12': '天津市', 
    '13': '河北省',
    '14': '山西省',
    '15': '内蒙古自治区',
    '21': '辽宁省',
    '22': '吉林省',
    '23': '黑龙江省',
    '31': '上海市',
    '32': '江苏省',
    '33': '浙江省',
    '34': '安徽省',
    '35': '福建省',
    '36': '江西省',
    '37': '山东省',
    '41': '河南省',
    '42': '湖北省',
    '43': '湖南省',
    '44': '广东省',
    '45': '广西壮族自治区',
    '46': '海南省',
    '50': '重庆市',
    '51': '四川省',
    '52': '贵州省',
    '53': '云南省',
    '54': '西藏自治区',
    '61': '陕西省',
    '62': '甘肃省',
    '63': '青海省',
    '64': '宁夏回族自治区',
    '65': '新疆维吾尔自治区'
  }
  
  return provinceNames[code] || `省份${code}`
}

/**
 * Calculate bounds for a set of features
 * @param {Array} features - GeoJSON features array
 * @returns {Array} Bounds array [minLng, minLat, maxLng, maxLat]
 */
export function calculateBounds(features) {
  let minLng = Infinity, minLat = Infinity
  let maxLng = -Infinity, maxLat = -Infinity
  
  features.forEach(feature => {
    const coords = feature.geometry.coordinates
    
    function processCoords(coordArray) {
      if (Array.isArray(coordArray[0])) {
        coordArray.forEach(processCoords)
      } else {
        const [lng, lat] = coordArray
        minLng = Math.min(minLng, lng)
        minLat = Math.min(minLat, lat)
        maxLng = Math.max(maxLng, lng)
        maxLat = Math.max(maxLat, lat)
      }
    }
    
    processCoords(coords)
  })
  
  return [minLng, minLat, maxLng, maxLat]
}
