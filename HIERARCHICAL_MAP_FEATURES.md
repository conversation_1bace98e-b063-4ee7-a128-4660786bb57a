# 🗺️ 分级地图显示功能说明

## 🎯 功能概述

实现了中国行政区划的分级显示系统，根据当前查看级别智能显示相应的地名和边界线，避免信息过载，提供清晰的地理信息展示。

## 📊 三级显示体系

### 1. 全国级别 (National Level)
**显示内容:**
- ✅ **省级边界** - 34个省级行政区轮廓
- ✅ **省级地名** - 只显示省、直辖市、自治区名称
- ✅ **省界线条** - 棕色边界线 (#8b7355)，较粗 (1.5px)

**视觉特征:**
- 字体大小: 12px
- 缩放级别: 1.2x
- 标签策略: 显示所有省级名称
- 边界颜色: 棕色 (省界标准色)

### 2. 省级级别 (Province Level)
**显示内容:**
- ✅ **市级边界** - 地级市、地区、自治州轮廓
- ✅ **市级地名** - 只显示市、地区、州名称
- ✅ **市界线条** - 蓝色边界线 (#5a9fd4)，中等粗细 (1.0px)

**视觉特征:**
- 字体大小: 10px
- 缩放级别: 1.5x
- 标签策略: 显示当前省内所有市级名称
- 边界颜色: 蓝色 (市界标准色)

### 3. 市级级别 (City Level)
**显示内容:**
- ✅ **县区边界** - 县、区、县级市轮廓
- ✅ **县区地名** - 只显示县、区、县级市名称
- ✅ **县界线条** - 橙色边界线 (#e67e22)，较细 (0.8px)

**视觉特征:**
- 字体大小: 9px
- 缩放级别: 2.0x
- 标签策略: 显示当前市内所有县区名称
- 边界颜色: 橙色 (县界标准色)

## 🎨 视觉设计规范

### 颜色体系
```css
/* 背景色 */
海洋背景: #a8d8ea (浅蓝色)
陆地填充: #fef7e6 (浅黄色)
悬停高亮: #fff2cc (淡黄色)

/* 边界线颜色分级 */
省界: #8b7355 (棕色) - 最高级别
市界: #5a9fd4 (蓝色) - 中级别  
县界: #e67e22 (橙色) - 基础级别

/* 文字颜色 */
标签文字: #333333 (深灰色)
悬停文字: #333333 (加粗显示)
```

### 字体规范
```css
/* 字体大小分级 */
省级标签: 12px (较大，突出显示)
市级标签: 10px (中等大小)
县级标签: 9px (较小，避免拥挤)

/* 字体样式 */
正常状态: normal weight
悬停状态: bold weight
字体族: system-ui, sans-serif
```

### 边界线规范
```css
/* 线条粗细分级 */
省界线: 1.5px (最粗，层级最高)
市界线: 1.0px (中等粗细)
县界线: 0.8px (最细，避免视觉干扰)

/* 悬停效果 */
悬停时: 原粗细 + 1px
悬停颜色: 对应颜色的深色版本
```

## 🔄 交互逻辑

### 钻取导航
```
全国视图 → 点击省份 → 省级视图 → 点击市区 → 市级视图
   ↑                        ↑                      ↑
   └── 返回全国 ←─── 返回省级 ←─── 返回市级 ←────────┘
```

### 面包屑导航
- **全国**: `全国`
- **省级**: `全国 > 北京市`
- **市级**: `全国 > 北京市 > 朝阳区`

### 控制按钮
- **返回按钮**: 逐级返回上一层
- **全国按钮**: 直接返回全国视图
- **层级指示**: 显示当前查看层级

## 📋 数据处理策略

### 1. 省级数据生成
```javascript
// 从县级数据聚合生成省级边界
const provinceFeatures = groupByProvinceCode(countyData)
echarts.registerMap('china', provinceFeatures)
```

### 2. 市级数据生成
```javascript
// 从县级数据提取市级边界
const cityFeatures = groupByCityCode(countyData, provinceCode)
echarts.registerMap(`province_${provinceCode}`, cityFeatures)
```

### 3. 县级数据使用
```javascript
// 直接使用原始县级数据
const countyFeatures = filterByCity(countyData, provinceCode, cityCode)
echarts.registerMap(`city_${provinceCode}_${cityCode}`, countyFeatures)
```

## 🎛️ 配置参数

### 缩放级别配置
```javascript
const zoomLevels = {
  national: 1.2,  // 全国视图
  province: 1.5,  // 省级视图
  city: 2.0       // 市级视图
}
```

### 标签显示配置
```javascript
const labelConfig = {
  national: { show: true, fontSize: 12 },  // 显示省名
  province: { show: true, fontSize: 10 },  // 显示市名
  city: { show: true, fontSize: 9 }        // 显示县名
}
```

### 边界样式配置
```javascript
const borderStyles = {
  national: { color: '#8b7355', width: 1.5 },  // 省界
  province: { color: '#5a9fd4', width: 1.0 },  // 市界
  city: { color: '#e67e22', width: 0.8 }       // 县界
}
```

## 🚀 性能优化

### 1. 按需加载
- 只注册当前需要的地图数据
- 避免一次性加载所有层级数据
- 实现懒加载机制

### 2. 数据缓存
- 缓存已生成的省市级数据
- 避免重复计算几何边界
- 使用 Map 结构优化查找

### 3. 渲染优化
- 根据缩放级别调整标签密度
- 动态调整边界线粗细
- 优化悬停和点击响应

## 📱 响应式适配

### 移动端优化
```css
@media (max-width: 768px) {
  /* 调整字体大小 */
  省级标签: 10px → 9px
  市级标签: 9px → 8px
  县级标签: 8px → 7px
  
  /* 调整边界线 */
  所有边界: 原粗细 × 0.8
  
  /* 调整控制面板 */
  按钮大小: 减小 20%
  面板位置: 自适应布局
}
```

## 🔍 用户体验

### 视觉层次
1. **清晰的层级区分** - 不同颜色和粗细的边界线
2. **适当的信息密度** - 避免标签重叠和视觉混乱
3. **直观的交互反馈** - 悬停高亮和点击响应

### 导航便利性
1. **多种返回方式** - 按钮、面包屑、双击
2. **状态指示** - 清楚显示当前位置
3. **快速定位** - 支持直接跳转到任意层级

### 信息获取
1. **渐进式展示** - 从宏观到微观的信息呈现
2. **上下文保持** - 始终知道当前位置
3. **详细信息** - 悬停显示额外信息

## 📈 扩展功能

### 计划中的改进
- 🔍 **搜索定位** - 支持地名搜索快速定位
- 📊 **数据统计** - 显示各级行政区数量统计
- 🎨 **主题切换** - 支持多种配色方案
- 📤 **导出功能** - 支持地图截图和数据导出
- 🌐 **多语言** - 支持中英文地名显示

### 技术优化
- ⚡ **WebGL 渲染** - 提升大数据量渲染性能
- 🗜️ **数据压缩** - 优化 GeoJSON 文件大小
- 🔄 **增量更新** - 支持地图数据动态更新
- 📱 **PWA 支持** - 离线使用和安装支持

---

**当前状态**: ✅ 基础功能已实现
**测试地址**: http://127.0.0.1:3000/
**更新时间**: 2024-06-25
