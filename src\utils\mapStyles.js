/**
 * Map styling utilities for Chinese administrative boundaries
 * Inspired by Tianditu administrative division styling
 */

/**
 * Get style configuration for different administrative levels
 * @param {string} level - Administrative level (province, city, county)
 * @param {string} state - Current state (default, hover, selected)
 * @returns {object} Leaflet style object
 */
export function getAdministrativeStyle(level, state = 'default') {
  const baseStyles = {
    province: {
      default: {
        fillColor: '#fef7e6',
        weight: 1.5,
        opacity: 1,
        color: '#8b7355',
        fillOpacity: 0.8,
        dashArray: null
      },
      hover: {
        fillColor: '#fff2cc',
        weight: 2,
        opacity: 1,
        color: '#6b5b47',
        fillOpacity: 0.9
      },
      selected: {
        fillColor: '#ffe066',
        weight: 2.5,
        opacity: 1,
        color: '#5a4a3a',
        fillOpacity: 0.95
      }
    },
    city: {
      default: {
        fillColor: '#f0f9ff',
        weight: 1,
        opacity: 0.9,
        color: '#7c3aed',
        fillOpacity: 0.7,
        dashArray: null
      },
      hover: {
        fillColor: '#e0f2fe',
        weight: 1.5,
        opacity: 1,
        color: '#6d28d9',
        fillOpacity: 0.8
      },
      selected: {
        fillColor: '#bae6fd',
        weight: 2,
        opacity: 1,
        color: '#5b21b6',
        fillOpacity: 0.85
      }
    },
    county: {
      default: {
        fillColor: '#fdf2f8',
        weight: 0.8,
        opacity: 0.8,
        color: '#be185d',
        fillOpacity: 0.6,
        dashArray: null
      },
      hover: {
        fillColor: '#fce7f3',
        weight: 1.2,
        opacity: 1,
        color: '#9d174d',
        fillOpacity: 0.75
      },
      selected: {
        fillColor: '#f9a8d4',
        weight: 1.5,
        opacity: 1,
        color: '#831843',
        fillOpacity: 0.8
      }
    }
  }

  return baseStyles[level]?.[state] || baseStyles.county.default
}

/**
 * Get heatmap gradient configuration
 * @param {string} theme - Theme name (default, blue, green, red)
 * @returns {object} Gradient configuration for leaflet.heat
 */
export function getHeatmapGradient(theme = 'default') {
  const gradients = {
    default: {
      0.0: '#313695',
      0.1: '#4575b4',
      0.2: '#74add1',
      0.3: '#abd9e9',
      0.4: '#e0f3f8',
      0.5: '#ffffcc',
      0.6: '#fee090',
      0.7: '#fdae61',
      0.8: '#f46d43',
      0.9: '#d73027',
      1.0: '#a50026'
    },
    blue: {
      0.0: '#f7fbff',
      0.2: '#deebf7',
      0.4: '#c6dbef',
      0.6: '#9ecae1',
      0.8: '#6baed6',
      1.0: '#2171b5'
    },
    green: {
      0.0: '#f7fcf5',
      0.2: '#e5f5e0',
      0.4: '#c7e9c0',
      0.6: '#a1d99b',
      0.8: '#74c476',
      1.0: '#238b45'
    },
    red: {
      0.0: '#fff5f0',
      0.2: '#fee0d2',
      0.4: '#fcbba1',
      0.6: '#fc9272',
      0.8: '#fb6a4a',
      1.0: '#cb181d'
    }
  }

  return gradients[theme] || gradients.default
}

/**
 * Get popup content HTML for administrative features
 * @param {object} feature - GeoJSON feature
 * @param {object} levelInfo - Parsed administrative level information
 * @returns {string} HTML content for popup
 */
export function getPopupContent(feature, levelInfo) {
  const { name, gb } = feature.properties
  
  let levelText = ''
  switch (levelInfo.level) {
    case 'province': levelText = '省/直辖市/自治区'; break
    case 'city': levelText = '市/地级市'; break
    case 'county': levelText = '县/区'; break
    default: levelText = '行政区'
  }

  return `
    <div class="admin-popup">
      <h4 class="popup-title">${name}</h4>
      <div class="popup-info">
        <div class="info-row">
          <span class="label">行政级别:</span>
          <span class="value">${levelText}</span>
        </div>
        <div class="info-row">
          <span class="label">行政代码:</span>
          <span class="value">${gb}</span>
        </div>
        <div class="info-row">
          <span class="label">省份代码:</span>
          <span class="value">${levelInfo.province}</span>
        </div>
        ${levelInfo.city ? `
          <div class="info-row">
            <span class="label">城市代码:</span>
            <span class="value">${levelInfo.city}</span>
          </div>
        ` : ''}
        ${levelInfo.county ? `
          <div class="info-row">
            <span class="label">县区代码:</span>
            <span class="value">${levelInfo.county}</span>
          </div>
        ` : ''}
      </div>
      <div class="popup-actions">
        <small>点击可钻取到下一级</small>
      </div>
    </div>
  `
}

/**
 * Get CSS styles for popups
 * @returns {string} CSS string
 */
export function getPopupStyles() {
  return `
    .admin-popup {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      min-width: 200px;
    }
    
    .popup-title {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      border-bottom: 2px solid #e5e7eb;
      padding-bottom: 8px;
    }
    
    .popup-info {
      margin-bottom: 12px;
    }
    
    .info-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 6px;
      font-size: 13px;
    }
    
    .info-row .label {
      color: #6b7280;
      font-weight: 500;
    }
    
    .info-row .value {
      color: #1f2937;
      font-weight: 600;
    }
    
    .popup-actions {
      text-align: center;
      padding-top: 8px;
      border-top: 1px solid #e5e7eb;
    }
    
    .popup-actions small {
      color: #9ca3af;
      font-size: 11px;
    }
  `
}

/**
 * Calculate appropriate zoom level based on administrative level and bounds
 * @param {string} level - Administrative level
 * @param {Array} bounds - Bounds array [minLng, minLat, maxLng, maxLat]
 * @returns {number} Appropriate zoom level
 */
export function calculateZoomLevel(level, bounds) {
  if (!bounds || bounds.length !== 4) return 4
  
  const [minLng, minLat, maxLng, maxLat] = bounds
  const lngDiff = maxLng - minLng
  const latDiff = maxLat - minLat
  const maxDiff = Math.max(lngDiff, latDiff)
  
  // Base zoom levels for different administrative levels
  const baseZooms = {
    province: 6,
    city: 8,
    county: 10
  }
  
  let zoom = baseZooms[level] || 6
  
  // Adjust based on geographic extent
  if (maxDiff > 10) zoom = Math.max(zoom - 2, 3)
  else if (maxDiff > 5) zoom = Math.max(zoom - 1, 4)
  else if (maxDiff < 1) zoom = Math.min(zoom + 2, 12)
  else if (maxDiff < 2) zoom = Math.min(zoom + 1, 11)
  
  return zoom
}

/**
 * Get animation options for map transitions
 * @param {string} type - Animation type (drillDown, goBack, reset)
 * @returns {object} Animation options for Leaflet
 */
export function getAnimationOptions(type = 'default') {
  const animations = {
    drillDown: {
      animate: true,
      duration: 0.8,
      easeLinearity: 0.25
    },
    goBack: {
      animate: true,
      duration: 0.6,
      easeLinearity: 0.3
    },
    reset: {
      animate: true,
      duration: 1.0,
      easeLinearity: 0.2
    },
    default: {
      animate: true,
      duration: 0.5,
      easeLinearity: 0.25
    }
  }
  
  return animations[type] || animations.default
}
