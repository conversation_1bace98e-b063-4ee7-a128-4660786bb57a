# Vue 2 中国行政区划地图组件使用说明

## 功能特点

✅ **已实现的功能：**
- 🗺️ 纯 GeoJSON 渲染的中国行政区划地图（无需瓦片服务）
- 🎯 省市县三级行政区划钻取功能
- 🔥 热力图数据叠加显示
- 🎨 仿天地图行政区划样式
- 📍 自动地名标注
- 🖱️ 交互式悬停和点击效果
- 📱 响应式设计
- ⚡ 加载进度指示和错误处理
- 🧭 面包屑导航
- 🎛️ 热力图控制面板

## 当前页面效果

页面已在 http://127.0.0.1:3000/ 启动，您可以看到：

1. **地图背景**: 海洋蓝色背景，无瓦片依赖
2. **行政区划**: 
   - 省级：浅黄色填充，棕色边界
   - 市级：浅蓝色填充，紫色边界  
   - 县级：浅粉色填充，深红边界
3. **地名标注**: 自动显示各级行政区名称
4. **交互功能**:
   - 悬停高亮效果
   - 点击钻取到下级行政区
   - 弹窗显示详细信息
5. **导航控制**:
   - 左上角返回按钮
   - 右上角面包屑导航
   - 底部操作提示
6. **热力图控制**: 右下角热力图设置面板

## 快速测试

### 1. 基本交互
- 鼠标悬停在任意省份上查看高亮效果
- 点击省份查看详细信息弹窗
- 点击省份进行钻取到市县级

### 2. 导航功能
- 使用左上角"返回"按钮回到上级
- 使用"全国"按钮回到全国视图
- 点击右上角面包屑快速导航

### 3. 热力图功能
- 右下角切换热力图显示/隐藏
- 调整强度和半径参数
- 切换不同颜色主题

## 数据格式

### GeoJSON 数据结构
```json
{
  "features": [
    {
      "geometry": {
        "type": "MultiPolygon",
        "coordinates": [...]
      },
      "properties": {
        "name": "北京市",
        "gb": "156110000"
      }
    }
  ]
}
```

### 热力图数据格式
```javascript
const heatmapData = [
  {
    lat: 39.9042,      // 纬度
    lng: 116.4074,     // 经度
    intensity: 0.8     // 强度 (0-1)
  }
]
```

## 组件配置

### 基本配置
```vue
<ChinaMap 
  :height="600"
  :tianditu-key="your-api-key"
  :heatmap-data="heatmapData"
  @province-click="onProvinceClick"
  @city-click="onCityClick"
/>
```

### 事件处理
```javascript
methods: {
  onProvinceClick(province) {
    console.log('省份:', province.name)
    console.log('代码:', province.gb)
    console.log('级别:', province.levelInfo.level)
  },
  
  onCityClick(city) {
    console.log('城市:', city.name)
  }
}
```

## 样式自定义

### CSS 变量
```css
.china-map-container {
  --province-fill: #fef7e6;
  --province-stroke: #8b7355;
  --city-fill: #f0f9ff;
  --city-stroke: #7c3aed;
  --county-fill: #fdf2f8;
  --county-stroke: #be185d;
}
```

### 标签样式
```css
.feature-label-province span {
  font-size: 16px !important;
  color: #1a202c !important;
}
```

## 技术实现

- **地图引擎**: Leaflet.js
- **热力图**: leaflet.heat 插件
- **数据处理**: 自定义 GeoJSON 解析器
- **样式系统**: 动态样式生成
- **标注系统**: Leaflet DivIcon
- **导航系统**: Vue 响应式状态管理

## 性能优化

1. **按需加载**: 根据当前级别只显示相关行政区
2. **样式缓存**: 预计算样式配置
3. **事件优化**: 防抖处理和事件委托
4. **内存管理**: 及时清理图层和事件监听器

## 浏览器兼容性

- Chrome 60+
- Firefox 55+  
- Safari 12+
- Edge 79+

## 下一步扩展

可以考虑添加的功能：
- 搜索定位功能
- 数据统计面板
- 导出功能（PNG/PDF）
- 多语言支持
- 自定义图标标注
- 动画过渡效果
