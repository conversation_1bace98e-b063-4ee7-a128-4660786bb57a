{"name": "vue-china-map", "version": "1.0.0", "description": "Interactive Chinese administrative map component for Vue 2", "main": "src/components/ChinaMap.vue", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vue-cli-service serve", "build-lib": "vue-cli-service build --target lib --name ChinaMap src/components/ChinaMap.vue"}, "dependencies": {"vue": "^2.7.14", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "@vue/composition-api": "^1.7.2"}, "devDependencies": {"@vitejs/plugin-vue2": "^2.3.1", "vite": "^5.0.0", "sass": "^1.69.5", "sass-loader": "^13.3.2"}, "keywords": ["vue", "vue2", "china", "map", "administrative", "tianditu", "g<PERSON><PERSON><PERSON>", "heatmap", "interactive"], "author": "Vue China Map", "license": "MIT"}