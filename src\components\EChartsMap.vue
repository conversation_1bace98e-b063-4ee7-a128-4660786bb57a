<template>
  <div class="echarts-map-container">
    <!-- Loading overlay -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>{{ loadingMessage }}</p>
      <div class="loading-progress" v-if="loadingProgress > 0">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: loadingProgress + '%' }"></div>
        </div>
        <span class="progress-text">{{ loadingProgress }}%</span>
      </div>
    </div>
    
    <!-- Error overlay -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h3>{{ errorTitle }}</h3>
        <p>{{ errorMessage }}</p>
        <div class="error-actions">
          <button @click="retryLoad" class="retry-button">重试加载</button>
          <button @click="resetComponent" class="reset-button">重置组件</button>
        </div>
      </div>
    </div>
    
    <!-- Map container -->
    <div 
      ref="mapContainer" 
      class="map-container"
      :style="{ height: height + 'px' }"
    ></div>
    
    <!-- Map controls -->
    <div class="map-controls">
      <button 
        v-if="currentLevel !== 'national'" 
        @click="goBack" 
        class="control-button back-button"
        title="返回上一级"
      >
        ← 返回
      </button>
      
      <button 
        @click="resetToNationalView" 
        class="control-button home-button"
        title="返回全国视图"
      >
        🏠 全国
      </button>
      
      <div class="level-indicator">
        当前层级: {{ levelText }}
      </div>
    </div>

    <!-- Breadcrumb navigation -->
    <div class="breadcrumb-nav" v-if="breadcrumbs.length > 1">
      <span 
        v-for="(crumb, index) in breadcrumbs" 
        :key="index"
        class="breadcrumb-item"
        :class="{ active: index === breadcrumbs.length - 1 }"
        @click="navigateToBreadcrumb(index)"
      >
        {{ crumb.name }}
        <span v-if="index < breadcrumbs.length - 1" class="breadcrumb-separator">></span>
      </span>
    </div>

    <!-- Heatmap controls -->
    <div class="heatmap-controls" v-if="heatmapData.length > 0">
      <div class="heatmap-toggle">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="heatmapVisible" 
            @change="toggleHeatmap"
          />
          <span class="toggle-text">热力图</span>
        </label>
      </div>
      
      <div class="heatmap-settings" v-if="heatmapVisible">
        <div class="setting-group">
          <label>强度:</label>
          <input
            type="range"
            min="0.1"
            max="2"
            step="0.1"
            v-model="heatmapIntensity"
            @input="updateHeatmapSettings"
          />
          <span class="setting-value">{{ heatmapIntensity }}</span>
        </div>

        <div class="setting-group">
          <label>大小:</label>
          <input
            type="range"
            min="5"
            max="50"
            step="5"
            v-model="heatmapSize"
            @input="updateHeatmapSettings"
          />
          <span class="setting-value">{{ heatmapSize }}</span>
        </div>

        <div class="setting-group">
          <label>透明度:</label>
          <input
            type="range"
            min="0.3"
            max="1"
            step="0.1"
            v-model="heatmapOpacity"
            @input="updateHeatmapSettings"
          />
          <span class="setting-value">{{ heatmapOpacity }}</span>
        </div>

        <div class="setting-group">
          <label class="toggle-label">
            <input
              type="checkbox"
              v-model="heatmapEffects"
              @change="updateHeatmapSettings"
            />
            <span>动画效果</span>
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'EChartsMap',
  props: {
    height: {
      type: Number,
      default: 600
    },
    heatmapData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      loading: true,
      error: false,
      errorTitle: '',
      errorMessage: '',
      loadingMessage: '初始化地图组件...',
      loadingProgress: 0,
      geoData: null,
      currentLevel: 'national', // 'national', 'province', 'city'
      currentProvince: null,
      currentCity: null,
      heatmapVisible: true,
      heatmapIntensity: 1.0,
      heatmapSize: 20,
      heatmapOpacity: 0.8,
      heatmapEffects: true,
      navigationHistory: []
    }
  },
  computed: {
    levelText() {
      switch (this.currentLevel) {
        case 'national': return '全国'
        case 'province': return this.currentProvince?.name || '省级'
        case 'city': return this.currentCity?.name || '市级'
        default: return '未知'
      }
    },
    breadcrumbs() {
      const crumbs = [{ name: '全国', level: 'national' }]
      
      if (this.currentProvince) {
        crumbs.push({ 
          name: this.currentProvince.name, 
          level: 'province',
          data: this.currentProvince 
        })
      }
      
      if (this.currentCity) {
        crumbs.push({ 
          name: this.currentCity.name, 
          level: 'city',
          data: this.currentCity 
        })
      }
      
      return crumbs
    }
  },
  mounted() {
    this.initializeChart()
    this.loadGeoData()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  watch: {
    heatmapData: {
      handler(newData) {
        this.updateHeatmap(newData)
      },
      deep: true
    }
  },
  methods: {
    initializeChart() {
      this.chart = echarts.init(this.$refs.mapContainer)
      
      // 设置图表点击事件
      this.chart.on('click', (params) => {
        if (params.componentType === 'geo') {
          this.handleRegionClick(params)
        }
      })
      
      // 设置图表悬停事件
      this.chart.on('mouseover', (params) => {
        if (params.componentType === 'geo') {
          this.chart.dispatchAction({
            type: 'highlight',
            geoIndex: 0,
            name: params.name
          })
        }
      })
      
      this.chart.on('mouseout', (params) => {
        if (params.componentType === 'geo') {
          this.chart.dispatchAction({
            type: 'downplay',
            geoIndex: 0,
            name: params.name
          })
        }
      })
    },

    async loadGeoData() {
      try {
        this.loading = true
        this.error = false
        this.loadingProgress = 0
        this.loadingMessage = '正在加载地理数据...'
        
        this.loadingProgress = 10
        
        // 加载 GeoJSON 文件
        const response = await fetch('/中国_县.geojson')
        this.loadingProgress = 30
        
        if (!response.ok) {
          throw new Error(`无法加载地理数据文件 (HTTP ${response.status})`)
        }
        
        this.loadingMessage = '正在解析地理数据...'
        this.loadingProgress = 50
        
        const data = await response.json()
        this.loadingProgress = 70
        
        if (!data || !data.features || !Array.isArray(data.features)) {
          throw new Error('地理数据格式不正确')
        }
        
        this.geoData = data
        this.loadingProgress = 80
        
        this.loadingMessage = '正在注册地图数据...'
        // 注册地图数据到 ECharts
        this.registerMapData()
        this.loadingProgress = 90
        
        this.loadingMessage = '正在渲染地图...'
        // 渲染地图
        this.renderMap()
        
        this.loadingProgress = 100
        setTimeout(() => {
          this.loading = false
        }, 300)
        
      } catch (err) {
        this.handleError('数据加载失败', err.message)
        console.error('Error loading geo data:', err)
      }
    },

    registerMapData() {
      // 处理省级数据
      const provinceFeatures = this.geoData.features.filter(feature => {
        const gb = feature.properties.gb
        return gb && gb.substring(7, 9) === '00' && gb.substring(5, 7) === '00'
      })
      
      if (provinceFeatures.length > 0) {
        echarts.registerMap('china', {
          type: 'FeatureCollection',
          features: provinceFeatures
        })
      }
      
      // 为每个省份注册市县级数据
      const provinces = this.getUniqueProvinces()
      provinces.forEach(province => {
        const cityFeatures = this.geoData.features.filter(feature => {
          const gb = feature.properties.gb
          return gb && gb.substring(3, 5) === province.code && gb.substring(7, 9) !== '00'
        })
        
        if (cityFeatures.length > 0) {
          echarts.registerMap(`province_${province.code}`, {
            type: 'FeatureCollection',
            features: cityFeatures
          })
        }
      })
    },

    getUniqueProvinces() {
      const provinceMap = new Map()
      
      this.geoData.features.forEach(feature => {
        const gb = feature.properties.gb
        if (gb && gb.length >= 5) {
          const provinceCode = gb.substring(3, 5)
          if (!provinceMap.has(provinceCode)) {
            // 查找省级feature
            const provinceFeature = this.geoData.features.find(f => 
              f.properties.gb === `156${provinceCode}0000`
            )
            
            if (provinceFeature) {
              provinceMap.set(provinceCode, {
                code: provinceCode,
                name: provinceFeature.properties.name,
                gbCode: provinceFeature.properties.gb
              })
            }
          }
        }
      })
      
      return Array.from(provinceMap.values())
    },

    renderMap() {
      const mapName = this.currentLevel === 'national' ? 'china' : 
                     this.currentLevel === 'province' ? `province_${this.currentProvince.code}` : 'china'
      
      const option = {
        backgroundColor: '#a8d8ea',
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            if (params.componentType === 'geo') {
              return `${params.name}<br/>点击查看详细信息`
            } else if (params.seriesType === 'scatter') {
              return `热力点<br/>强度: ${params.value[2]}`
            }
            return params.name
          }
        },
        geo: {
          map: mapName,
          roam: true,
          zoom: 1.2,
          center: this.currentLevel === 'national' ? [104.1954, 35.8617] : undefined,
          itemStyle: {
            areaColor: '#fef7e6',
            borderColor: '#8b7355',
            borderWidth: 1
          },
          emphasis: {
            itemStyle: {
              areaColor: '#fff2cc',
              borderColor: '#6b5b47',
              borderWidth: 2
            },
            label: {
              show: true,
              color: '#333',
              fontSize: 12,
              fontWeight: 'bold'
            }
          },
          label: {
            show: true,
            color: '#333',
            fontSize: 10,
            fontWeight: 'normal'
          }
        },
        series: []
      }

      // 添加热力图数据 - 使用散点图代替热力图
      if (this.heatmapVisible && this.heatmapData.length > 0) {
        // 添加 visualMap 组件
        option.visualMap = {
          min: 0,
          max: 1,
          calculable: true,
          inRange: {
            color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
          },
          textStyle: {
            color: '#333'
          },
          right: 20,
          bottom: 100
        }

        // 添加基础散点图
        option.series.push({
          type: 'scatter',
          coordinateSystem: 'geo',
          data: this.heatmapData.map(item => [item.lng, item.lat, item.intensity * this.heatmapIntensity]),
          symbolSize: (val) => {
            return Math.max(8, val[2] * this.heatmapSize)
          },
          itemStyle: {
            opacity: this.heatmapOpacity,
            shadowBlur: 10,
            shadowColor: 'rgba(255, 0, 0, 0.5)'
          },
          emphasis: {
            itemStyle: {
              opacity: Math.min(1, this.heatmapOpacity + 0.2)
            }
          }
        })

        // 添加效果散点图增强热力图效果（仅在开启动画效果时）
        if (this.heatmapEffects) {
          option.series.push({
            type: 'effectScatter',
            coordinateSystem: 'geo',
            data: this.heatmapData.filter(item => item.intensity > 0.6).map(item => [item.lng, item.lat, item.intensity * this.heatmapIntensity]),
            symbolSize: (val) => {
              return Math.max(12, val[2] * this.heatmapSize * 0.8)
            },
            showEffectOn: 'render',
            rippleEffect: {
              brushType: 'stroke',
              scale: 2.5,
              period: 4
            },
            itemStyle: {
              opacity: this.heatmapOpacity * 0.8,
              shadowBlur: 15,
              shadowColor: 'rgba(255, 100, 0, 0.6)'
            },
            emphasis: {
              scale: true
            }
          })
        }
      }
      
      this.chart.setOption(option, true)
    },

    handleRegionClick(params) {
      const regionName = params.name
      
      if (this.currentLevel === 'national') {
        // 钻取到省级
        const province = this.getUniqueProvinces().find(p => p.name === regionName)
        if (province) {
          this.drillDownToProvince(province)
        }
      } else if (this.currentLevel === 'province') {
        // 钻取到市级（如果有数据）
        this.drillDownToCity({ name: regionName })
      }
      
      // 发射事件
      this.$emit(`${this.currentLevel}-click`, {
        name: regionName,
        level: this.currentLevel
      })
    },

    drillDownToProvince(province) {
      this.currentLevel = 'province'
      this.currentProvince = province
      this.renderMap()
    },

    drillDownToCity(city) {
      this.currentLevel = 'city'
      this.currentCity = city
      // 市级暂时不重新渲染，只更新状态
    },

    goBack() {
      if (this.currentLevel === 'city') {
        this.currentLevel = 'province'
        this.currentCity = null
      } else if (this.currentLevel === 'province') {
        this.resetToNationalView()
      }
    },

    resetToNationalView() {
      this.currentLevel = 'national'
      this.currentProvince = null
      this.currentCity = null
      this.renderMap()
    },

    navigateToBreadcrumb(index) {
      const crumb = this.breadcrumbs[index]
      
      if (crumb.level === 'national') {
        this.resetToNationalView()
      } else if (crumb.level === 'province' && this.currentLevel !== 'province') {
        this.currentLevel = 'province'
        this.currentCity = null
        this.renderMap()
      }
    },

    updateHeatmap() {
      this.renderMap()
    },

    updateHeatmapSettings() {
      if (this.heatmapData.length > 0) {
        this.renderMap()
      }
    },

    toggleHeatmap() {
      this.renderMap()
    },

    handleError(title, message) {
      this.loading = false
      this.errorTitle = title
      this.errorMessage = message
      this.error = true
    },

    retryLoad() {
      this.loadGeoData()
    },

    resetComponent() {
      this.loading = true
      this.error = false
      this.errorTitle = ''
      this.errorMessage = ''
      this.currentLevel = 'national'
      this.currentProvince = null
      this.currentCity = null
      
      setTimeout(() => {
        this.loadGeoData()
      }, 500)
    }
  }
}
</script>

<style scoped>
.echarts-map-container {
  position: relative;
  width: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.map-container {
  width: 100%;
  position: relative;
  z-index: 1;
}

/* Loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay p {
  color: #666;
  font-size: 14px;
  margin: 0 0 10px 0;
}

.loading-progress {
  width: 200px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3498db;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 30px;
}

/* Error overlay */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-content {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 300px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-content h3 {
  color: #e74c3c;
  margin: 0 0 10px 0;
  font-size: 18px;
}

.error-content p {
  color: #666;
  margin: 0 0 20px 0;
  font-size: 14px;
}

.error-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.retry-button, .reset-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.retry-button:hover, .reset-button:hover {
  background: #2980b9;
}

.reset-button {
  background: #95a5a6;
}

.reset-button:hover {
  background: #7f8c8d;
}

/* Map controls */
.map-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-button {
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  white-space: nowrap;
}

.control-button:hover {
  background: #f8f9fa;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.back-button {
  background: #17a2b8;
  color: white;
  border-color: #17a2b8;
}

.back-button:hover {
  background: #138496;
  border-color: #117a8b;
}

.home-button {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.home-button:hover {
  background: #218838;
  border-color: #1e7e34;
}

.level-indicator {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  margin-top: 4px;
}

/* Breadcrumb navigation */
.breadcrumb-nav {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  max-width: 300px;
}

.breadcrumb-item {
  cursor: pointer;
  color: #007bff;
  text-decoration: none;
  transition: color 0.3s;
}

.breadcrumb-item:hover {
  color: #0056b3;
  text-decoration: underline;
}

.breadcrumb-item.active {
  color: #6c757d;
  cursor: default;
  font-weight: 600;
}

.breadcrumb-item.active:hover {
  text-decoration: none;
}

.breadcrumb-separator {
  margin: 0 6px;
  color: #6c757d;
}

/* Heatmap controls */
.heatmap-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 12px;
  min-width: 200px;
}

.heatmap-toggle {
  margin-bottom: 12px;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.toggle-label input[type="checkbox"] {
  margin: 0;
}

.heatmap-settings {
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
}

.setting-group {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
}

.setting-group label {
  min-width: 40px;
  color: #666;
  font-weight: 500;
}

.setting-group input[type="range"] {
  flex: 1;
  margin: 0;
}

.setting-value {
  min-width: 30px;
  text-align: right;
  color: #333;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .map-controls {
    top: 5px;
    left: 5px;
  }

  .control-button {
    padding: 6px 10px;
    font-size: 11px;
  }

  .level-indicator {
    font-size: 11px;
    padding: 4px 8px;
  }

  .breadcrumb-nav {
    top: 5px;
    right: 5px;
    font-size: 11px;
    padding: 6px 10px;
  }

  .heatmap-controls {
    bottom: 10px;
    right: 10px;
    min-width: 150px;
  }
}
</style>
