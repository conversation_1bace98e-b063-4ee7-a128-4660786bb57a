<template>
  <div class="echarts-map-container">
    <!-- Loading overlay -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>{{ loadingMessage }}</p>
      <div class="loading-progress" v-if="loadingProgress > 0">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: loadingProgress + '%' }"></div>
        </div>
        <span class="progress-text">{{ loadingProgress }}%</span>
      </div>
    </div>
    
    <!-- Error overlay -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h3>{{ errorTitle }}</h3>
        <p>{{ errorMessage }}</p>
        <div class="error-actions">
          <button @click="retryLoad" class="retry-button">重试加载</button>
          <button @click="resetComponent" class="reset-button">重置组件</button>
        </div>
      </div>
    </div>
    
    <!-- Map container -->
    <div 
      ref="mapContainer" 
      class="map-container"
      :style="{ height: height + 'px' }"
    ></div>
    
    <!-- Map controls -->
    <div class="map-controls">
      <button 
        v-if="currentLevel !== 'national'" 
        @click="goBack" 
        class="control-button back-button"
        title="返回上一级"
      >
        ← 返回
      </button>
      
      <button 
        @click="resetToNationalView" 
        class="control-button home-button"
        title="返回全国视图"
      >
        🏠 全国
      </button>
      
      <div class="level-indicator">
        当前层级: {{ levelText }}
      </div>
    </div>

    <!-- Breadcrumb navigation -->
    <div class="breadcrumb-nav" v-if="breadcrumbs.length > 1">
      <span 
        v-for="(crumb, index) in breadcrumbs" 
        :key="index"
        class="breadcrumb-item"
        :class="{ active: index === breadcrumbs.length - 1 }"
        @click="navigateToBreadcrumb(index)"
      >
        {{ crumb.name }}
        <span v-if="index < breadcrumbs.length - 1" class="breadcrumb-separator">></span>
      </span>
    </div>

    <!-- Heatmap controls -->
    <div class="heatmap-controls" v-if="heatmapData.length > 0">
      <div class="heatmap-toggle">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="heatmapVisible" 
            @change="toggleHeatmap"
          />
          <span class="toggle-text">热力图</span>
        </label>
      </div>
      
      <div class="heatmap-settings" v-if="heatmapVisible">
        <div class="setting-group">
          <label>强度:</label>
          <input
            type="range"
            min="0.1"
            max="2"
            step="0.1"
            v-model="heatmapIntensity"
            @input="updateHeatmapSettings"
          />
          <span class="setting-value">{{ heatmapIntensity }}</span>
        </div>

        <div class="setting-group">
          <label>大小:</label>
          <input
            type="range"
            min="5"
            max="50"
            step="5"
            v-model="heatmapSize"
            @input="updateHeatmapSettings"
          />
          <span class="setting-value">{{ heatmapSize }}</span>
        </div>

        <div class="setting-group">
          <label>透明度:</label>
          <input
            type="range"
            min="0.3"
            max="1"
            step="0.1"
            v-model="heatmapOpacity"
            @input="updateHeatmapSettings"
          />
          <span class="setting-value">{{ heatmapOpacity }}</span>
        </div>

        <div class="setting-group">
          <label class="toggle-label">
            <input
              type="checkbox"
              v-model="heatmapEffects"
              @change="updateHeatmapSettings"
            />
            <span>动画效果</span>
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'EChartsMap',
  props: {
    height: {
      type: Number,
      default: 600
    },
    heatmapData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      loading: true,
      error: false,
      errorTitle: '',
      errorMessage: '',
      loadingMessage: '初始化地图组件...',
      loadingProgress: 0,
      geoData: null,
      currentLevel: 'national', // 'national', 'province', 'city'
      currentProvince: null,
      currentCity: null,
      heatmapVisible: true,
      heatmapIntensity: 1.0,
      heatmapSize: 20,
      heatmapOpacity: 0.8,
      heatmapEffects: true,
      navigationHistory: []
    }
  },
  computed: {
    levelText() {
      switch (this.currentLevel) {
        case 'national': return '全国'
        case 'province': return this.currentProvince?.name || '省级'
        case 'city': return this.currentCity?.name || '市级'
        default: return '未知'
      }
    },
    breadcrumbs() {
      const crumbs = [{ name: '全国', level: 'national' }]
      
      if (this.currentProvince) {
        crumbs.push({ 
          name: this.currentProvince.name, 
          level: 'province',
          data: this.currentProvince 
        })
      }
      
      if (this.currentCity) {
        crumbs.push({ 
          name: this.currentCity.name, 
          level: 'city',
          data: this.currentCity 
        })
      }
      
      return crumbs
    }
  },
  mounted() {
    this.initializeChart()
    this.loadGeoData()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  watch: {
    heatmapData: {
      handler(newData) {
        this.updateHeatmap(newData)
      },
      deep: true
    }
  },
  methods: {
    initializeChart() {
      this.chart = echarts.init(this.$refs.mapContainer)
      
      // 设置图表点击事件
      this.chart.on('click', (params) => {
        if (params.componentType === 'geo') {
          this.handleRegionClick(params)
        }
      })
      
      // 设置图表悬停事件
      this.chart.on('mouseover', (params) => {
        if (params.componentType === 'geo') {
          this.chart.dispatchAction({
            type: 'highlight',
            geoIndex: 0,
            name: params.name
          })
        }
      })
      
      this.chart.on('mouseout', (params) => {
        if (params.componentType === 'geo') {
          this.chart.dispatchAction({
            type: 'downplay',
            geoIndex: 0,
            name: params.name
          })
        }
      })
    },

    async loadGeoData() {
      try {
        this.loading = true
        this.error = false
        this.loadingProgress = 0
        this.loadingMessage = '正在加载地理数据...'
        
        this.loadingProgress = 10
        
        // 加载 GeoJSON 文件
        const response = await fetch('/中国_县.geojson')
        this.loadingProgress = 30
        
        if (!response.ok) {
          throw new Error(`无法加载地理数据文件 (HTTP ${response.status})`)
        }
        
        this.loadingMessage = '正在解析地理数据...'
        this.loadingProgress = 50
        
        const data = await response.json()
        this.loadingProgress = 70
        
        if (!data || !data.features || !Array.isArray(data.features)) {
          throw new Error('地理数据格式不正确')
        }
        
        this.geoData = data
        this.loadingProgress = 80
        
        this.loadingMessage = '正在注册地图数据...'
        // 注册地图数据到 ECharts
        this.registerMapData()
        this.loadingProgress = 90
        
        this.loadingMessage = '正在渲染地图...'
        // 渲染地图
        this.renderMap()
        
        this.loadingProgress = 100
        setTimeout(() => {
          this.loading = false
        }, 300)
        
      } catch (err) {
        this.handleError('数据加载失败', err.message)
        console.error('Error loading geo data:', err)
      }
    },

    registerMapData() {
      console.log('开始注册地图数据，总特征数:', this.geoData.features.length)

      // 分级注册地图数据
      this.registerNationalMap()
      this.registerProvinceMap()
      this.registerCityMaps()
    },

    registerNationalMap() {
      // 注册全国地图 - 创建省级边界
      const provinces = this.getUniqueProvinces()
      const provinceFeatures = []

      provinces.forEach(province => {
        // 获取该省份的所有县级数据
        const countyFeatures = this.geoData.features.filter(feature => {
          const gb = feature.properties.gb
          return gb && gb.substring(3, 5) === province.code
        })

        if (countyFeatures.length > 0) {
          // 创建省级特征 - 合并所有县级几何形状
          const provinceFeature = {
            type: 'Feature',
            properties: {
              name: province.name,
              gb: province.gbCode,
              level: 'province',
              provinceCode: province.code
            },
            geometry: this.createProvinceGeometry(countyFeatures)
          }
          provinceFeatures.push(provinceFeature)
        }
      })

      echarts.registerMap('china', {
        type: 'FeatureCollection',
        features: provinceFeatures
      })
      console.log('已注册全国地图，省份数:', provinceFeatures.length)
      console.log('省份列表:', provinceFeatures.map(f => f.properties.name))
    },

    createProvinceGeometry(countyFeatures) {
      // 简化版本：合并所有县级几何形状为省级边界
      const allCoordinates = []

      countyFeatures.forEach(feature => {
        if (feature.geometry) {
          if (feature.geometry.type === 'Polygon') {
            allCoordinates.push(feature.geometry.coordinates)
          } else if (feature.geometry.type === 'MultiPolygon') {
            allCoordinates.push(...feature.geometry.coordinates)
          }
        }
      })

      return {
        type: 'MultiPolygon',
        coordinates: allCoordinates
      }
    },

    registerProvinceMap() {
      // 注册省级地图 - 显示市级边界
      const provinces = this.getUniqueProvinces()

      provinces.forEach(province => {
        const cityFeatures = this.getCityFeatures(province.code)

        if (cityFeatures.length > 0) {
          echarts.registerMap(`province_${province.code}`, {
            type: 'FeatureCollection',
            features: cityFeatures
          })
          console.log(`已注册省份 ${province.name} 地图，市级区域数: ${cityFeatures.length}`)
        }
      })
    },

    registerCityMaps() {
      // 注册市级地图 - 显示县区边界
      const provinces = this.getUniqueProvinces()

      provinces.forEach(province => {
        const cities = this.getCitiesInProvince(province.code)

        cities.forEach(city => {
          const countyFeatures = this.geoData.features.filter(feature => {
            const gb = feature.properties.gb
            return gb && gb.substring(3, 5) === province.code &&
                   gb.substring(5, 7) === city.code
          })

          if (countyFeatures.length > 0) {
            echarts.registerMap(`city_${province.code}_${city.code}`, {
              type: 'FeatureCollection',
              features: countyFeatures
            })
          }
        })
      })
    },

    mergeGeometries(features) {
      // 简化版本：返回第一个特征的几何形状
      // 实际应用中应该合并所有几何形状
      return features[0]?.geometry || null
    },

    getCityFeatures(provinceCode) {
      // 获取省内的市级特征
      const cityMap = new Map()

      this.geoData.features.forEach(feature => {
        const gb = feature.properties.gb
        if (gb && gb.substring(3, 5) === provinceCode) {
          const cityCode = gb.substring(5, 7)
          if (cityCode !== '00') {
            if (!cityMap.has(cityCode)) {
              // 创建市级特征
              cityMap.set(cityCode, {
                type: 'Feature',
                properties: {
                  name: this.getCityName(provinceCode, cityCode),
                  gb: `156${provinceCode}${cityCode}00`,
                  level: 'city',
                  cityCode: cityCode
                },
                geometry: {
                  type: 'MultiPolygon',
                  coordinates: []
                }
              })
            }

            // 收集该市的所有县级几何形状
            const city = cityMap.get(cityCode)
            if (feature.geometry && feature.geometry.coordinates) {
              if (feature.geometry.type === 'Polygon') {
                city.geometry.coordinates.push(feature.geometry.coordinates)
              } else if (feature.geometry.type === 'MultiPolygon') {
                city.geometry.coordinates.push(...feature.geometry.coordinates)
              }
            }
          }
        }
      })

      // 过滤掉没有几何数据的市
      const cityFeatures = Array.from(cityMap.values()).filter(city =>
        city.geometry.coordinates.length > 0
      )

      console.log(`省份 ${provinceCode} 的市级数据:`, cityFeatures.map(c => c.properties.name))
      return cityFeatures
    },

    getCitiesInProvince(provinceCode) {
      const citySet = new Set()

      this.geoData.features.forEach(feature => {
        const gb = feature.properties.gb
        if (gb && gb.substring(3, 5) === provinceCode) {
          const cityCode = gb.substring(5, 7)
          if (cityCode !== '00') {
            citySet.add(cityCode)
          }
        }
      })

      return Array.from(citySet).map(code => ({
        code,
        name: this.getCityName(provinceCode, code)
      }))
    },

    getCityName(provinceCode, cityCode) {
      // 从县级数据中推断市名
      const cityFeatures = this.geoData.features.filter(feature => {
        const gb = feature.properties.gb
        return gb && gb.substring(3, 5) === provinceCode && gb.substring(5, 7) === cityCode
      })

      if (cityFeatures.length > 0) {
        // 收集该市下所有县的名称
        const countyNames = cityFeatures.map(f => f.properties.name)
        console.log(`市代码 ${cityCode} 下的县:`, countyNames)

        // 尝试从县名推断市名
        const firstCounty = countyNames[0]

        // 如果县名包含市名，提取市名
        if (firstCounty.includes('市')) {
          const parts = firstCounty.split('市')
          if (parts.length > 1) {
            return parts[0] + '市'
          }
        }

        // 处理地区、州等行政单位
        if (firstCounty.includes('地区')) {
          return firstCounty.split('地区')[0] + '地区'
        } else if (firstCounty.includes('州')) {
          return firstCounty.split('州')[0] + '州'
        } else if (firstCounty.includes('盟')) {
          return firstCounty.split('盟')[0] + '盟'
        }

        // 默认返回第一个县的名称去掉县区字
        return firstCounty.replace(/[县区]$/, '') + '市'
      }

      return `未知市${cityCode}`
    },

    getUniqueProvinces() {
      const provinceMap = new Map()
      const provinceNames = {
        '11': '北京市', '12': '天津市', '13': '河北省', '14': '山西省', '15': '内蒙古自治区',
        '21': '辽宁省', '22': '吉林省', '23': '黑龙江省', '31': '上海市', '32': '江苏省',
        '33': '浙江省', '34': '安徽省', '35': '福建省', '36': '江西省', '37': '山东省',
        '41': '河南省', '42': '湖北省', '43': '湖南省', '44': '广东省', '45': '广西壮族自治区',
        '46': '海南省', '50': '重庆市', '51': '四川省', '52': '贵州省', '53': '云南省',
        '54': '西藏自治区', '61': '陕西省', '62': '甘肃省', '63': '青海省', '64': '宁夏回族自治区',
        '65': '新疆维吾尔自治区', '71': '台湾省', '81': '香港特别行政区', '82': '澳门特别行政区'
      }

      // 从县级数据中提取省份代码
      this.geoData.features.forEach(feature => {
        const gb = feature.properties.gb
        if (gb && gb.length >= 5) {
          const provinceCode = gb.substring(3, 5)
          if (!provinceMap.has(provinceCode) && provinceNames[provinceCode]) {
            provinceMap.set(provinceCode, {
              code: provinceCode,
              name: provinceNames[provinceCode],
              gbCode: `156${provinceCode}0000`
            })
          }
        }
      })

      const provinces = Array.from(provinceMap.values())
      console.log('获取到的省份列表:', provinces.map(p => p.name))
      return provinces
    },

    renderMap() {
      const mapName = this.getMapName()
      console.log('渲染地图:', mapName, '当前级别:', this.currentLevel)

      const option = {
        backgroundColor: '#a8d8ea',
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            if (params.componentType === 'geo') {
              return `${params.name}<br/>点击查看详细信息`
            } else if (params.seriesType === 'scatter') {
              return `热力点<br/>强度: ${params.value[2]}`
            }
            return params.name
          }
        },
        geo: {
          map: mapName,
          roam: true,
          zoom: this.getZoomLevel(),
          center: this.getMapCenter(),
          itemStyle: this.getItemStyle(),
          emphasis: this.getEmphasisStyle(),
          label: this.getLabelStyle()
        },
        series: []
      }

      // 添加热力图数据 - 使用散点图代替热力图
      if (this.heatmapVisible && this.heatmapData.length > 0) {
        // 添加 visualMap 组件
        option.visualMap = {
          min: 0,
          max: 1,
          calculable: true,
          inRange: {
            color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
          },
          textStyle: {
            color: '#333'
          },
          right: 20,
          bottom: 100
        }

        // 添加基础散点图
        option.series.push({
          type: 'scatter',
          coordinateSystem: 'geo',
          data: this.heatmapData.map(item => [item.lng, item.lat, item.intensity * this.heatmapIntensity]),
          symbolSize: (val) => {
            return Math.max(8, val[2] * this.heatmapSize)
          },
          itemStyle: {
            opacity: this.heatmapOpacity,
            shadowBlur: 10,
            shadowColor: 'rgba(255, 0, 0, 0.5)'
          },
          emphasis: {
            itemStyle: {
              opacity: Math.min(1, this.heatmapOpacity + 0.2)
            }
          }
        })

        // 添加效果散点图增强热力图效果（仅在开启动画效果时）
        if (this.heatmapEffects) {
          option.series.push({
            type: 'effectScatter',
            coordinateSystem: 'geo',
            data: this.heatmapData.filter(item => item.intensity > 0.6).map(item => [item.lng, item.lat, item.intensity * this.heatmapIntensity]),
            symbolSize: (val) => {
              return Math.max(12, val[2] * this.heatmapSize * 0.8)
            },
            showEffectOn: 'render',
            rippleEffect: {
              brushType: 'stroke',
              scale: 2.5,
              period: 4
            },
            itemStyle: {
              opacity: this.heatmapOpacity * 0.8,
              shadowBlur: 15,
              shadowColor: 'rgba(255, 100, 0, 0.6)'
            },
            emphasis: {
              scale: true
            }
          })
        }
      }
      
      console.log('设置图表选项:', option)
      this.chart.setOption(option, true)
      console.log('图表选项已设置')
    },

    handleRegionClick(params) {
      const regionName = params.name
      console.log('点击区域:', regionName, '当前级别:', this.currentLevel)
      console.log('点击参数:', params)

      if (this.currentLevel === 'national') {
        // 在全国级别，点击的应该是省份
        const provinces = this.getUniqueProvinces()
        const targetProvince = provinces.find(p => p.name === regionName)

        if (targetProvince) {
          console.log('钻取到省份:', targetProvince.name)
          this.drillDownToProvince(targetProvince)
        } else {
          console.log('未找到省份:', regionName)
          console.log('可用省份:', provinces.map(p => p.name))
        }
      } else if (this.currentLevel === 'province') {
        // 在省级，点击的应该是市
        console.log('钻取到市级:', regionName)
        this.drillDownToCity({ name: regionName })
      } else if (this.currentLevel === 'city') {
        // 在市级，点击的是县区，暂时只显示信息
        console.log('点击县区:', regionName)
      }

      // 发射事件
      this.$emit(`${this.currentLevel}-click`, {
        name: regionName,
        level: this.currentLevel
      })
    },

    drillDownToProvince(province) {
      this.currentLevel = 'province'
      this.currentProvince = province
      this.renderMap()
    },

    drillDownToCity(city) {
      // 从市名推断市代码
      const cityCode = this.getCityCodeFromName(city.name)
      if (cityCode) {
        this.currentLevel = 'city'
        this.currentCity = {
          name: city.name,
          code: cityCode
        }
        this.renderMap()
      }
    },

    getCityCodeFromName(cityName) {
      // 从数据中查找市代码
      const feature = this.geoData.features.find(f => {
        const name = f.properties.name
        return name && (name.includes(cityName) || cityName.includes(name.split(/[市县区]/)[0]))
      })

      if (feature && feature.properties.gb) {
        return feature.properties.gb.substring(5, 7)
      }

      return null
    },

    goBack() {
      if (this.currentLevel === 'city') {
        this.currentLevel = 'province'
        this.currentCity = null
        this.renderMap()
      } else if (this.currentLevel === 'province') {
        this.resetToNationalView()
      }
    },

    resetToNationalView() {
      this.currentLevel = 'national'
      this.currentProvince = null
      this.currentCity = null
      this.renderMap()
    },

    navigateToBreadcrumb(index) {
      const crumb = this.breadcrumbs[index]
      
      if (crumb.level === 'national') {
        this.resetToNationalView()
      } else if (crumb.level === 'province' && this.currentLevel !== 'province') {
        this.currentLevel = 'province'
        this.currentCity = null
        this.renderMap()
      }
    },

    updateHeatmap() {
      this.renderMap()
    },

    updateHeatmapSettings() {
      if (this.heatmapData.length > 0) {
        this.renderMap()
      }
    },

    toggleHeatmap() {
      this.renderMap()
    },

    handleError(title, message) {
      this.loading = false
      this.errorTitle = title
      this.errorMessage = message
      this.error = true
    },

    retryLoad() {
      this.loadGeoData()
    },

    resetComponent() {
      this.loading = true
      this.error = false
      this.errorTitle = ''
      this.errorMessage = ''
      this.currentLevel = 'national'
      this.currentProvince = null
      this.currentCity = null
      
      setTimeout(() => {
        this.loadGeoData()
      }, 500)
    },

    getMapName() {
      switch (this.currentLevel) {
        case 'national':
          return 'china'
        case 'province':
          return `province_${this.currentProvince.code}`
        case 'city':
          return `city_${this.currentProvince.code}_${this.currentCity.code}`
        default:
          return 'china'
      }
    },

    getZoomLevel() {
      switch (this.currentLevel) {
        case 'national': return 1.2
        case 'province': return 1.5
        case 'city': return 2.0
        default: return 1.2
      }
    },

    getMapCenter() {
      if (this.currentLevel === 'national') {
        return [104.1954, 35.8617]
      }
      // 对于省市级，让 ECharts 自动计算中心点
      return undefined
    },

    getItemStyle() {
      const baseStyle = {
        areaColor: '#fef7e6',
        borderColor: this.getBorderColor(),
        borderWidth: this.getBorderWidth()
      }
      return baseStyle
    },

    getEmphasisStyle() {
      return {
        itemStyle: {
          areaColor: '#fff2cc',
          borderColor: '#6b5b47',
          borderWidth: this.getBorderWidth() + 1
        },
        label: {
          show: true,
          color: '#333',
          fontSize: this.getLabelFontSize() + 2,
          fontWeight: 'bold'
        }
      }
    },

    getLabelStyle() {
      return {
        show: true,
        color: '#333',
        fontSize: this.getLabelFontSize(),
        fontWeight: this.currentLevel === 'national' ? 'bold' : 'normal',
        // 根据级别调整标签显示策略
        formatter: this.currentLevel === 'national' ? '{b}' : '{b}'
      }
    },

    getBorderColor() {
      switch (this.currentLevel) {
        case 'national': return '#2c5aa0'  // 省界 - 深蓝色，更明显
        case 'province': return '#5a9fd4'  // 市界 - 蓝色
        case 'city': return '#e67e22'      // 县界 - 橙色
        default: return '#2c5aa0'
      }
    },

    getBorderWidth() {
      switch (this.currentLevel) {
        case 'national': return 2.0  // 省界更粗
        case 'province': return 1.2  // 市界中等
        case 'city': return 0.8      // 县界较细
        default: return 1.5
      }
    },

    getLabelFontSize() {
      switch (this.currentLevel) {
        case 'national': return 14   // 省名更大，更明显
        case 'province': return 11   // 市名中等
        case 'city': return 9        // 县名较小
        default: return 12
      }
    }
  }
}
</script>

<style scoped>
.echarts-map-container {
  position: relative;
  width: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.map-container {
  width: 100%;
  position: relative;
  z-index: 1;
}

/* Loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay p {
  color: #666;
  font-size: 14px;
  margin: 0 0 10px 0;
}

.loading-progress {
  width: 200px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3498db;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 30px;
}

/* Error overlay */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-content {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 300px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-content h3 {
  color: #e74c3c;
  margin: 0 0 10px 0;
  font-size: 18px;
}

.error-content p {
  color: #666;
  margin: 0 0 20px 0;
  font-size: 14px;
}

.error-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.retry-button, .reset-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.retry-button:hover, .reset-button:hover {
  background: #2980b9;
}

.reset-button {
  background: #95a5a6;
}

.reset-button:hover {
  background: #7f8c8d;
}

/* Map controls */
.map-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-button {
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  white-space: nowrap;
}

.control-button:hover {
  background: #f8f9fa;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.back-button {
  background: #17a2b8;
  color: white;
  border-color: #17a2b8;
}

.back-button:hover {
  background: #138496;
  border-color: #117a8b;
}

.home-button {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.home-button:hover {
  background: #218838;
  border-color: #1e7e34;
}

.level-indicator {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  margin-top: 4px;
}

/* Breadcrumb navigation */
.breadcrumb-nav {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  max-width: 300px;
}

.breadcrumb-item {
  cursor: pointer;
  color: #007bff;
  text-decoration: none;
  transition: color 0.3s;
}

.breadcrumb-item:hover {
  color: #0056b3;
  text-decoration: underline;
}

.breadcrumb-item.active {
  color: #6c757d;
  cursor: default;
  font-weight: 600;
}

.breadcrumb-item.active:hover {
  text-decoration: none;
}

.breadcrumb-separator {
  margin: 0 6px;
  color: #6c757d;
}

/* Heatmap controls */
.heatmap-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 12px;
  min-width: 200px;
}

.heatmap-toggle {
  margin-bottom: 12px;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.toggle-label input[type="checkbox"] {
  margin: 0;
}

.heatmap-settings {
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
}

.setting-group {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
}

.setting-group label {
  min-width: 40px;
  color: #666;
  font-weight: 500;
}

.setting-group input[type="range"] {
  flex: 1;
  margin: 0;
}

.setting-value {
  min-width: 30px;
  text-align: right;
  color: #333;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .map-controls {
    top: 5px;
    left: 5px;
  }

  .control-button {
    padding: 6px 10px;
    font-size: 11px;
  }

  .level-indicator {
    font-size: 11px;
    padding: 4px 8px;
  }

  .breadcrumb-nav {
    top: 5px;
    right: 5px;
    font-size: 11px;
    padding: 6px 10px;
  }

  .heatmap-controls {
    bottom: 10px;
    right: 10px;
    min-width: 150px;
  }
}
</style>
