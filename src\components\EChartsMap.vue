<template>
  <div class="echarts-map-container">
    <!-- Loading overlay -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>{{ loadingMessage }}</p>
      <div class="loading-progress" v-if="loadingProgress > 0">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: loadingProgress + '%' }"></div>
        </div>
        <span class="progress-text">{{ loadingProgress }}%</span>
      </div>
    </div>
    
    <!-- Error overlay -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h3>{{ errorTitle }}</h3>
        <p>{{ errorMessage }}</p>
        <div class="error-actions">
          <button @click="retryLoad" class="retry-button">重试加载</button>
          <button @click="resetComponent" class="reset-button">重置组件</button>
        </div>
      </div>
    </div>
    
    <!-- Map container -->
    <div 
      ref="mapContainer" 
      class="map-container"
      :style="{ height: height + 'px' }"
    ></div>
    
    <!-- Map controls -->
    <div class="map-controls">
      <button 
        v-if="currentLevel !== 'national'" 
        @click="goBack" 
        class="control-button back-button"
        title="返回上一级"
      >
        ← 返回
      </button>
      
      <button 
        @click="resetToNationalView" 
        class="control-button home-button"
        title="返回全国视图"
      >
        🏠 全国
      </button>
      
      <div class="level-indicator">
        当前层级: {{ levelText }}
      </div>
    </div>

    <!-- Breadcrumb navigation -->
    <div class="breadcrumb-nav" v-if="breadcrumbs.length > 1">
      <span 
        v-for="(crumb, index) in breadcrumbs" 
        :key="index"
        class="breadcrumb-item"
        :class="{ active: index === breadcrumbs.length - 1 }"
        @click="navigateToBreadcrumb(index)"
      >
        {{ crumb.name }}
        <span v-if="index < breadcrumbs.length - 1" class="breadcrumb-separator">></span>
      </span>
    </div>

    <!-- Heatmap controls -->
    <div class="heatmap-controls" v-if="heatmapData.length > 0">
      <div class="heatmap-toggle">
        <label class="toggle-label">
          <input 
            type="checkbox" 
            v-model="heatmapVisible" 
            @change="toggleHeatmap"
          />
          <span class="toggle-text">热力图</span>
        </label>
      </div>
      
      <div class="heatmap-settings" v-if="heatmapVisible">
        <div class="setting-group">
          <label>强度:</label>
          <input 
            type="range" 
            min="0.1" 
            max="2" 
            step="0.1" 
            v-model="heatmapIntensity"
            @input="updateHeatmapSettings"
          />
          <span class="setting-value">{{ heatmapIntensity }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'EChartsMap',
  props: {
    height: {
      type: Number,
      default: 600
    },
    heatmapData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      loading: true,
      error: false,
      errorTitle: '',
      errorMessage: '',
      loadingMessage: '初始化地图组件...',
      loadingProgress: 0,
      geoData: null,
      currentLevel: 'national', // 'national', 'province', 'city'
      currentProvince: null,
      currentCity: null,
      heatmapVisible: true,
      heatmapIntensity: 1.0,
      navigationHistory: []
    }
  },
  computed: {
    levelText() {
      switch (this.currentLevel) {
        case 'national': return '全国'
        case 'province': return this.currentProvince?.name || '省级'
        case 'city': return this.currentCity?.name || '市级'
        default: return '未知'
      }
    },
    breadcrumbs() {
      const crumbs = [{ name: '全国', level: 'national' }]
      
      if (this.currentProvince) {
        crumbs.push({ 
          name: this.currentProvince.name, 
          level: 'province',
          data: this.currentProvince 
        })
      }
      
      if (this.currentCity) {
        crumbs.push({ 
          name: this.currentCity.name, 
          level: 'city',
          data: this.currentCity 
        })
      }
      
      return crumbs
    }
  },
  mounted() {
    this.initializeChart()
    this.loadGeoData()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  watch: {
    heatmapData: {
      handler(newData) {
        this.updateHeatmap(newData)
      },
      deep: true
    }
  },
  methods: {
    initializeChart() {
      this.chart = echarts.init(this.$refs.mapContainer)
      
      // 设置图表点击事件
      this.chart.on('click', (params) => {
        if (params.componentType === 'geo') {
          this.handleRegionClick(params)
        }
      })
      
      // 设置图表悬停事件
      this.chart.on('mouseover', (params) => {
        if (params.componentType === 'geo') {
          this.chart.dispatchAction({
            type: 'highlight',
            geoIndex: 0,
            name: params.name
          })
        }
      })
      
      this.chart.on('mouseout', (params) => {
        if (params.componentType === 'geo') {
          this.chart.dispatchAction({
            type: 'downplay',
            geoIndex: 0,
            name: params.name
          })
        }
      })
    },

    async loadGeoData() {
      try {
        this.loading = true
        this.error = false
        this.loadingProgress = 0
        this.loadingMessage = '正在加载地理数据...'
        
        this.loadingProgress = 10
        
        // 加载 GeoJSON 文件
        const response = await fetch('/中国_县.geojson')
        this.loadingProgress = 30
        
        if (!response.ok) {
          throw new Error(`无法加载地理数据文件 (HTTP ${response.status})`)
        }
        
        this.loadingMessage = '正在解析地理数据...'
        this.loadingProgress = 50
        
        const data = await response.json()
        this.loadingProgress = 70
        
        if (!data || !data.features || !Array.isArray(data.features)) {
          throw new Error('地理数据格式不正确')
        }
        
        this.geoData = data
        this.loadingProgress = 80
        
        this.loadingMessage = '正在注册地图数据...'
        // 注册地图数据到 ECharts
        this.registerMapData()
        this.loadingProgress = 90
        
        this.loadingMessage = '正在渲染地图...'
        // 渲染地图
        this.renderMap()
        
        this.loadingProgress = 100
        setTimeout(() => {
          this.loading = false
        }, 300)
        
      } catch (err) {
        this.handleError('数据加载失败', err.message)
        console.error('Error loading geo data:', err)
      }
    },

    registerMapData() {
      // 处理省级数据
      const provinceFeatures = this.geoData.features.filter(feature => {
        const gb = feature.properties.gb
        return gb && gb.substring(7, 9) === '00' && gb.substring(5, 7) === '00'
      })
      
      if (provinceFeatures.length > 0) {
        echarts.registerMap('china', {
          type: 'FeatureCollection',
          features: provinceFeatures
        })
      }
      
      // 为每个省份注册市县级数据
      const provinces = this.getUniqueProvinces()
      provinces.forEach(province => {
        const cityFeatures = this.geoData.features.filter(feature => {
          const gb = feature.properties.gb
          return gb && gb.substring(3, 5) === province.code && gb.substring(7, 9) !== '00'
        })
        
        if (cityFeatures.length > 0) {
          echarts.registerMap(`province_${province.code}`, {
            type: 'FeatureCollection',
            features: cityFeatures
          })
        }
      })
    },

    getUniqueProvinces() {
      const provinceMap = new Map()
      
      this.geoData.features.forEach(feature => {
        const gb = feature.properties.gb
        if (gb && gb.length >= 5) {
          const provinceCode = gb.substring(3, 5)
          if (!provinceMap.has(provinceCode)) {
            // 查找省级feature
            const provinceFeature = this.geoData.features.find(f => 
              f.properties.gb === `156${provinceCode}0000`
            )
            
            if (provinceFeature) {
              provinceMap.set(provinceCode, {
                code: provinceCode,
                name: provinceFeature.properties.name,
                gbCode: provinceFeature.properties.gb
              })
            }
          }
        }
      })
      
      return Array.from(provinceMap.values())
    },

    renderMap() {
      const mapName = this.currentLevel === 'national' ? 'china' : 
                     this.currentLevel === 'province' ? `province_${this.currentProvince.code}` : 'china'
      
      const option = {
        backgroundColor: '#a8d8ea',
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            return `${params.name}<br/>点击查看详细信息`
          }
        },
        geo: {
          map: mapName,
          roam: true,
          zoom: 1.2,
          center: this.currentLevel === 'national' ? [104.1954, 35.8617] : undefined,
          itemStyle: {
            areaColor: '#fef7e6',
            borderColor: '#8b7355',
            borderWidth: 1
          },
          emphasis: {
            itemStyle: {
              areaColor: '#fff2cc',
              borderColor: '#6b5b47',
              borderWidth: 2
            },
            label: {
              show: true,
              color: '#333',
              fontSize: 12,
              fontWeight: 'bold'
            }
          },
          label: {
            show: true,
            color: '#333',
            fontSize: 10,
            fontWeight: 'normal'
          }
        },
        series: []
      }
      
      // 添加热力图数据
      if (this.heatmapVisible && this.heatmapData.length > 0) {
        option.series.push({
          type: 'heatmap',
          coordinateSystem: 'geo',
          data: this.heatmapData.map(item => [item.lng, item.lat, item.intensity * this.heatmapIntensity]),
          pointSize: 20,
          blurSize: 35
        })
      }
      
      this.chart.setOption(option, true)
    },

    handleRegionClick(params) {
      const regionName = params.name
      
      if (this.currentLevel === 'national') {
        // 钻取到省级
        const province = this.getUniqueProvinces().find(p => p.name === regionName)
        if (province) {
          this.drillDownToProvince(province)
        }
      } else if (this.currentLevel === 'province') {
        // 钻取到市级（如果有数据）
        this.drillDownToCity({ name: regionName })
      }
      
      // 发射事件
      this.$emit(`${this.currentLevel}-click`, {
        name: regionName,
        level: this.currentLevel
      })
    },

    drillDownToProvince(province) {
      this.currentLevel = 'province'
      this.currentProvince = province
      this.renderMap()
    },

    drillDownToCity(city) {
      this.currentLevel = 'city'
      this.currentCity = city
      // 市级暂时不重新渲染，只更新状态
    },

    goBack() {
      if (this.currentLevel === 'city') {
        this.currentLevel = 'province'
        this.currentCity = null
      } else if (this.currentLevel === 'province') {
        this.resetToNationalView()
      }
    },

    resetToNationalView() {
      this.currentLevel = 'national'
      this.currentProvince = null
      this.currentCity = null
      this.renderMap()
    },

    navigateToBreadcrumb(index) {
      const crumb = this.breadcrumbs[index]
      
      if (crumb.level === 'national') {
        this.resetToNationalView()
      } else if (crumb.level === 'province' && this.currentLevel !== 'province') {
        this.currentLevel = 'province'
        this.currentCity = null
        this.renderMap()
      }
    },

    updateHeatmap() {
      this.renderMap()
    },

    updateHeatmapSettings() {
      if (this.heatmapData.length > 0) {
        this.renderMap()
      }
    },

    toggleHeatmap() {
      this.renderMap()
    },

    handleError(title, message) {
      this.loading = false
      this.errorTitle = title
      this.errorMessage = message
      this.error = true
    },

    retryLoad() {
      this.loadGeoData()
    },

    resetComponent() {
      this.loading = true
      this.error = false
      this.errorTitle = ''
      this.errorMessage = ''
      this.currentLevel = 'national'
      this.currentProvince = null
      this.currentCity = null
      
      setTimeout(() => {
        this.loadGeoData()
      }, 500)
    }
  }
}
</script>
