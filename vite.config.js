import { defineConfig } from 'vite'
import { createVuePlugin } from '@vitejs/plugin-vue2'

export default defineConfig({
  plugins: [createVuePlugin()],
  server: {
    port: 3000,
    open: true
  },
  build: {
    lib: {
      entry: 'src/components/ChinaMap.vue',
      name: 'ChinaMap',
      fileName: 'china-map'
    },
    rollupOptions: {
      external: ['vue'],
      output: {
        globals: {
          vue: 'Vue'
        }
      }
    }
  }
})
